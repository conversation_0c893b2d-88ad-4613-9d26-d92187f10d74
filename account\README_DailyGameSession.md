# Daily Game Session Model

The `DailyGameSession` model tracks daily game sessions for each member, creating one record per member per day when they sync with the server.

## Model Overview

### Purpose
- Track daily user engagement and session patterns
- Monitor device and platform usage
- Analyze game version adoption
- Provide data for user behavior analytics

### Key Features
- **One session per member per day**: Unique constraint ensures only one record per member per date
- **Automatic creation**: Sessions are created/updated during member sync
- **Session tracking**: Tracks sync count, first sync time, and last sync time
- **Device & platform info**: Stores device, game version, and market data

## Model Fields

### Core Fields
- `member`: Foreign key to Member model
- `date`: Date of the game session
- `device`: Device information from member sync
- `game_version`: Game version from member sync  
- `market`: Market/platform from member data

### Session Tracking
- `first_sync_time`: When the member first synced on this date (auto-set on creation)
- `last_sync_time`: When the member last synced on this date (auto-updated)
- `sync_count`: Number of times member synced on this date (incremented on each sync)

### Calculated Properties
- `session_duration_minutes`: Duration between first and last sync in minutes
- `is_active_session`: True if last sync was within 30 minutes

## Database Design

### Constraints
- **Unique together**: (`member`, `date`) - ensures one session per member per day
- **Indexes**: Optimized for common queries on date, member+date, market+date, game_version+date

### Performance
- Efficient querying for daily analytics
- Indexed fields for fast filtering and aggregation
- Minimal storage overhead

## Integration

### Automatic Creation
Sessions are automatically created/updated in the `sync_member` function:

```python
# In account/function_views.py - sync_member function
today = timezone.now().date()
session, created = DailyGameSession.objects.get_or_create(
    member=member,
    date=today,
    defaults={
        'device': member.device,
        'game_version': member.game_version,
        'market': member.market,
    }
)

if not created:
    # Update existing session
    session.device = member.device
    session.game_version = member.game_version
    session.market = member.market
    session.sync_count += 1
    session.save()
```

### Behavior
1. **First sync of the day**: Creates new DailyGameSession record
2. **Subsequent syncs**: Updates existing record with latest device/version info and increments sync_count
3. **Data consistency**: Always reflects the latest device, game version, and market from the member

## Admin Interface

The model is fully integrated with Django admin:
- List view with key session information
- Filtering by date, market, game version
- Search by member username/handle and device
- Readonly calculated fields
- Raw ID field for member selection

## Use Cases

### Analytics Queries

**Daily active users by market:**
```python
from django.db.models import Count
sessions = DailyGameSession.objects.filter(date='2025-09-03')
market_stats = sessions.values('market').annotate(count=Count('member')).order_by('-count')
```

**Game version adoption:**
```python
version_stats = DailyGameSession.objects.filter(date='2025-09-03').values('game_version').annotate(count=Count('member')).order_by('-count')
```

**User engagement patterns:**
```python
# Users with multiple syncs (high engagement)
active_users = DailyGameSession.objects.filter(date='2025-09-03', sync_count__gt=5)

# Session duration analysis
long_sessions = DailyGameSession.objects.filter(date='2025-09-03').exclude(session_duration_minutes=0).order_by('-session_duration_minutes')
```

### Reporting
- Daily user engagement reports
- Platform/market distribution analysis
- Game version migration tracking
- User session pattern analysis

## Data Retention

The model stores historical session data for analytics. Consider implementing cleanup policies based on your data retention requirements:

```python
# Example: Clean up sessions older than 1 year
from datetime import date, timedelta
cutoff_date = date.today() - timedelta(days=365)
DailyGameSession.objects.filter(date__lt=cutoff_date).delete()
```

## Benefits

1. **Comprehensive tracking**: Captures all daily user interactions
2. **Performance optimized**: Efficient database design for analytics
3. **Automatic maintenance**: No manual intervention required
4. **Rich analytics**: Enables detailed user behavior analysis
5. **Platform insights**: Track user distribution across markets and devices
