import random
import json
import logging
from rest_framework import permissions
from rest_framework.decorators import api_view, permission_classes, renderer_classes
from rest_framework_swagger import renderers
from django.http.response import JsonResponse
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.core.validators import validate_email
from django.db.models import Sum, Q
from datetime import timed<PERSON><PERSON>

from account.utils import is_key_valid
from shop.models import CharacterOwn
from shop.serializers import CharacterSerializer
from .models.sync_data import SyncData, SiteElement
from .models.member import Member, MemberData, MARKET_MYKET, PRESENCE_MAIN_MENU, ContentCreator, MARKET_BAZAAR, MARKET_ZARINPAL, MARKET_GOOGLEPLAY, MARKET_DESKTOP
from .models.daily_game_session import DailyGameSession
from .serializers import MemberSerializer, MemberDataSerializer, SyncDataSerializer, MetaProgressSerializer, MemberBanSerializer, SiteElementSerializer
from utils.utils import number_generator, send_email, inventory_check, get_ip_from_request
from .models.meta_progress import PRIZE_TYPE_COIN, PRIZE_TYPE_CHARACTER, MetaProgressLevel
from game.models import PurchaseWithCoin, AddCoin, OnlineStatistics
from shop.models import Purchase
from mobile.models import MobilePhone, ClanLevel
from mobile.serializers import MobilePhoneSerializer, ClanLevelSerializer, ClanSerializer


logger = logging.getLogger('django')


@api_view(['POST', 'GET'])
@permission_classes([])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def register_member(request):
    data = request.data
    
    member = Member()
    member.init(data.get("market", MARKET_MYKET), data.get("device", ""))

    coin_log = AddCoin()
    coin_log.member = member
    coin_log.value = 200
    coin_log.type = "StartMoney"
    coin_log.save()

    return JsonResponse(status=200, data=MemberSerializer(member).data)


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def sync_member(request):
    user = request.user
    data = request.data
    member: Member = request.user
    member.last_login = timezone.now()
    member.game_version = data.get("game_version", 0)
    member.device = data.get("device", "")

    if not is_key_valid(member, data.get("key", "")):
        #send_email.delay("<EMAIL>", "Key Error", member.username + " " + str(member.id))
        logger.info("Secret Key Error: " + str(member.id))
        return JsonResponse(status=400, data={"message": "GTFO"})
    
    if member.market == MARKET_DESKTOP:
        if member.desktop_verified == False:
            send_email.delay("<EMAIL>", "GTFO", member.username + " " + str(member.id))
            return JsonResponse(status=400, data={"message": "GTFO"})
    


    current_market = member.market
    new_market = data.get("market", None)
    if new_market is not None:
        logger.info("sync market = " + new_market)
        if member.switch_to_zarinpal == False:
            if new_market != current_market:
                if new_market == MARKET_ZARINPAL:
                    member.switch_to_zarinpal = True
                    member.coin += 300
        member.market = new_market
    member.save()

    # Create or update daily game session
    today = timezone.now().date()
    session, created = DailyGameSession.objects.get_or_create(
        member=member,
        date=today,
        defaults={
            'device': member.device,
            'game_version': member.game_version,
            'market': member.market,
        }
    )

    if not created:
        # Update existing session
        session.device = member.device
        session.game_version = member.game_version
        session.market = member.market
        session.sync_count += 1
        session.save()

    sync_data = SyncData.objects.get(id=1)

    owns = CharacterOwn.objects.filter(member=user)
    characters = []
    for own in owns:
        characters.append(own.character)
    

    progress_levels = MetaProgressLevel.objects.all().order_by("level")

    mobile_phones = MobilePhone.objects.all().order_by("id")
    clan_levels = ClanLevel.objects.all().order_by("level")
    clan = None
    if member.clan != None:
        clan = ClanSerializer(member.clan).data
    

    onlines = OnlineStatistics.objects.all().order_by('-id')[0]
    public_chat = "PublicChat" + str(random.randint(1, sync_data.public_chat_channels))

    member_data = MemberDataSerializer(member.data.all()[0]).data
    if inventory_check(member, member_data["data"]):#Ban if use mode
       logger.info("Inventory Ban: " + member.username + " : " + json.dumps(member_data))
    # logger.info("Inventory Ban: " + str(member_data))

    result = {
        "member": MemberSerializer(member).data,
        "data": member_data,
        "sync": SyncDataSerializer(sync_data).data,
        "characters": CharacterSerializer(characters, many=True).data,
        "progresses": MetaProgressSerializer(progress_levels, many=True).data,
        "phones": MobilePhoneSerializer(mobile_phones, many=True).data,
        "clan_levels": ClanLevelSerializer(clan_levels, many=True).data,
        "clan": clan,
        "online_count": onlines.total_online_players,
        "public_chat": public_chat,
    }
    return JsonResponse(status=200, data=result)


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def save_data(request):
    data = request.data
    member = request.user

    data.pop("cup", 0)
    data.pop("coin", 0)
    member.handle = data["handle"]
    member.hunger = data.get("hunger", 100)
    member.save()
    member_data = member.data.all()[0]
    member_data.data_string = json.dumps(data)
    member_data.save()
    return JsonResponse(status=200, data={"message": "success"})


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def change_handle(request):
    data = request.data
    member = request.user

    if len(data.get("handle", "")) > 14:
        return JsonResponse(status=400, data={"message": "Name Too Long"})

    if member.name_changed_count > 0:
        sync_data = SyncData.objects.get(id=1)
        if sync_data.change_name_price > member.coin:
            return JsonResponse(status=400, data={"message": "Not Enough Coin"})
        member.coin -= sync_data.change_name_price
        purchase_log = PurchaseWithCoin()
        purchase_log.member = member
        purchase_log.price = sync_data.change_name_price
        purchase_log.forgiving = False
        purchase_log.type = "ChangeHandle"
        purchase_log.secondary = member.title
        purchase_log.success = True
        purchase_log.save()


    member.handle = data["handle"]
    member.name_changed_count += 1
    member.log_data = member.log_data + "handle: " + member.handle +"\n"
    member.save()
    return JsonResponse(status=200, data={"message": "success"})


@api_view(['POST', ])
@permission_classes([])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def tutorial_finished(request):
    member = request.user
    member.tutorial_finished = True
    member.save()
    return JsonResponse(status=200, data={})


@api_view(['POST', ])
@permission_classes([])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def tutorial_started(request):
    member = request.user
    member.tutorial_started = True
    member.save()
    return JsonResponse(status=200, data={})


@api_view(['POST', ])
@permission_classes([])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def nakama_connect(request):
    member = request.user
    member.nakama_connect = True
    member.save()
    return JsonResponse(status=200, data={})


@api_view(['POST', ])
@permission_classes([])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def server_test_fail(request):
    member = request.user
    member.server_test_fail = True
    member.save()
    return JsonResponse(status=200, data={})


@api_view(['POST', ])
@permission_classes([])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def connect_account_with_email(request):
    data = request.data
    user = request.user

    email = data.get("email", "").lower()
    try:
        validate_email(email)
    except ValidationError as e:
        logger.info("Invalid Email: " + email)
        return JsonResponse(status=400, data={"message": "Email is not valid."})
    
    user.email_code = number_generator(5)
    user.save()

    send_email.delay(email, "Animal Rush", "کد ورود شما: {}".format(user.email_code))

    return JsonResponse(status=200, data={"message": "Your Code sent to your email"})


@api_view(['POST', ])
@permission_classes([])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def verify_account_code(request):
    data = request.data
    user = request.user

    email = data.get("email", "").lower()
    code = str(data.get("code", ""))

    member = Member.objects.filter(email=email)
    if len(member) == 0:
        #Should link account to member
        if user.email_code != code:
            logger.info("Incorrect code: " + str(code))
            return JsonResponse(status=400, data={"message": "Wrong code"})
        user.email = email
        user.email_code = number_generator(20)
        user.log_data = user.log_data + "email: " + email +"\n"
        user.save()
        return JsonResponse(status=201, data={"message": "Your Account linked succesfuly"})
    
    member = member[0]
    if user.email_code != code:
        ip = get_ip_from_request(request)
        logger.info(ip + ": " + member.username + " Incorrect code: " + str(code))
        user.email_code = number_generator(20)
        user.save()
        return JsonResponse(status=400, data={"message": "Wrong code"})

    user.linked_member = member
    user.email_code = number_generator(20)
    member.email_code = number_generator(20)
    user.save()
    member.save()
    return JsonResponse(status=200, data=
                        {
                            "message": "Reload app",
                            "token": member.token
                        })


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def claim_progress_prize(request):
    user = request.user
    progression = user.progress_level

    if user.is_meta_progress_finished == False:
        return JsonResponse(status=400, data={"message": "Progression is not done"})

    if progression.prize_type == PRIZE_TYPE_COIN:
        user.coin += progression.prize_coin
        user.progress_level = progression.next
        user.save()

        coin_log = AddCoin()
        coin_log.member = user
        coin_log.value = progression.prize_coin
        coin_log.type = "Progression"
        coin_log.secondary = str(user.progress_level)
        coin_log.save()

        return JsonResponse(status=200, data=
                            {
                                "type": "coin",
                                "prize_coin": progression.prize_coin,
                                "coin": user.coin,
                                "progress": MetaProgressSerializer(user.progress_level).data,
                                "is_finished": user.is_meta_progress_finished,
                            })
    
    if progression.prize_type == PRIZE_TYPE_CHARACTER:
        owns = CharacterOwn.objects.filter(member=user, character=progression.prize_character)
        if len(owns) > 0:
            print(progression.prize_character, "already unlocked")
        else:
            own = CharacterOwn()
            own.member = user
            own.character = progression.prize_character
            own.save()
        user.progress_level = progression.next
        user.save()
        return JsonResponse(status=200, data=
                        {
                            "type": "character",
                            "character": CharacterSerializer(progression.prize_character).data,
                            "coin": user.coin,
                            "progress": MetaProgressSerializer(user.progress_level).data,
                            "is_finished": user.is_meta_progress_finished,
                        })
    
    
    return JsonResponse(status=401, data={"message": "INVALID PROGRESSION"})


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def am_i_ban(request):
    return JsonResponse(status=200, data={
        "member": MemberBanSerializer(request.user).data,
    })


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def update_presence(request):
    member = request.user
    data = request.data

    member.presence = data.get("presence", PRESENCE_MAIN_MENU)
    member.presence_last_update = timezone.now()
    member.presence_data = data.get("presence_data", "")
    member.save()

    return JsonResponse(status=200, data={})


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def request_remove_email(request):
    member = request.user

    # Check if user has an email to remove
    if not member.email:
        return JsonResponse(status=400, data={"message": "No email address found to remove"})

    # Generate remove code and send email
    member.remove_code = number_generator(6)
    member.save()

    # Send email with remove code
    send_email.delay(member.email, "Animal Rush - Email Removal",
                    "کد حذف ایمیل شما: {}".format(member.remove_code))

    # Log the request
    member.log_data = member.log_data + "email_remove_request: " + member.email + " " + member.remove_code + "\n"
    member.save()

    return JsonResponse(status=200, data={"message": "Remove code sent to your email"})


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def verify_remove_email(request):
    data = request.data
    member = request.user

    code = str(data.get("code", ""))

    # Check if user has an email to remove
    if not member.email:
        member.log_data = member.log_data + "email_remove_verify_failed: no_email\n"
        member.save()
        return JsonResponse(status=400, data={"message": "No email address found to remove"})

    # Verify the remove code
    if member.remove_code != code or not member.remove_code:
        # Log invalid code attempt
        member.log_data = member.log_data + "email_remove_verify_failed: invalid_code=" + code + "\n"
        member.save()
        logger.info("Invalid remove code: " + str(code) + " for member: " + str(member.id))
        return JsonResponse(status=400, data={"message": "Invalid remove code"})

    # Valid code - remove email
    old_email = member.email
    member.email = ""
    member.remove_code = ""

    # Log successful email removal
    member.log_data = member.log_data + "email_remove_verify_success: " + old_email + "\n"
    member.save()

    return JsonResponse(status=200, data={"message": "Email removed successfully"})


@api_view(['POST', ])
@permission_classes([])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def remove_email(request):
    member = request.user

    member.email = ""
    member.email_code = number_generator(20)
    member.log_data = member.log_data + "email: REMOVE" + "\n"

    member.save()


    return JsonResponse(status=200, data={"message": "Success"})


@api_view(['POST', ])
@permission_classes([])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def list_site_elements(request):
    elements = SiteElement.objects.filter(visible=True).order_by('order')

    return JsonResponse(status=200, data={"elements": SiteElementSerializer(elements, many=True).data})


@api_view(['POST', ])
@permission_classes([])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def ami_content_creator(request):
    member = request.user

    result = ContentCreator.objects.filter(member=member, enabled=True)
    if len(result) == 0:
        meta = None
        referer = member.referer_content_creator
        if referer != None:
            meta = {
                "referer_handle": referer.member.handle,
                "referer_code": referer.code,
            }
        result = {
            "content_creator": False,
            "meta": meta
        }
        logger.info(result)
        return JsonResponse(status=200, data=result)
    
    #Content Creator#

    result = result[0]
    referer_count = Member.objects.filter(referer_content_creator=result).count()
    return JsonResponse(status=200, data={
        "content_creator": True,
        "code": result.code,
        "referer_count": referer_count,
    })


@api_view(['POST', ])
@permission_classes([])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def refer_content_creator(request):
    member = request.user
    data = request.data

    creators = ContentCreator.objects.filter(enabled=True, code=data.get("code", ""))
    if len(creators) == 0:
        return JsonResponse(status=400, data={"message": "Not Found"})
    
    creator = creators[0]
    if member.referer_content_creator != None:
        return JsonResponse(status=401, data={"message": "Already has referer"})
    
    member.referer_content_creator = creator
    member.save()
    return JsonResponse(status=200, data={"message": "Success"})


@api_view(['POST', ])
@permission_classes([])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def content_creator_stats(request):
    member = request.user
    data = request.data

    creators = ContentCreator.objects.filter(member=member, enabled=True)
    if len(creators) == 0:
        return JsonResponse(status=400, data={"message": "Not Found"})
    
    stats = {}
    creator = creators[0]

    if creator.is_coin:
        coins = int(creator.coin_sum * creator.coin_share / 100)
        count = creator.purchase_count
        return JsonResponse(status=200, data={
            "is_coin": True,
            "count": count,
            "coins": coins,
    })

    diff_time = timedelta(hours=1)
    mykets = Purchase.objects.filter(is_test=False, referer_content_creator=creator, market=MARKET_MYKET, created__gt=creator.last_cashout)
    sum = mykets.aggregate(sum=Sum('item__price_tag'))["sum"]
    if sum == None:
        sum = 0
    count = mykets.count()
    stats["myket"] = {
        "count": count,
        "sum": sum,
        "share": creator.myket_share,
        "revenue": int(creator.myket_share * sum / 100),
    }

    bazaars = Purchase.objects.filter(is_test=False, referer_content_creator=creator, market=MARKET_BAZAAR, created__gt=creator.last_cashout)
    sum = bazaars.aggregate(sum=Sum('item__price_tag'))["sum"]
    if sum == None:
        sum = 0
    count = bazaars.count()
    stats["bazaar"] = {
        "count": count,
        "sum": sum,
        "share": creator.bazaar_share,
        "revenue": int(creator.bazaar_share * sum / 100),
    }


    zarinpal = Purchase.objects.filter(is_test=False, referer_content_creator=creator, market=MARKET_ZARINPAL, created__gt=creator.last_cashout).filter(Q(zarinpal_code=100) | Q(zarinpal_code=101))
    sum = zarinpal.aggregate(sum=Sum('paid_price'))["sum"]
    if sum == None:
        sum = 0
    count = zarinpal.count()
    stats["zarinpal"] = {
        "count": count,
        "sum": sum,
        "share": creator.zarinpal_share,
        "revenue": int(creator.zarinpal_share * sum / 100),
    }

    google = Purchase.objects.filter(is_test=False, referer_content_creator=creator, market=MARKET_GOOGLEPLAY, created__gt=creator.last_cashout)
    sum = google.aggregate(sum=Sum('item__price_tag'))["sum"]
    if sum == None:
        sum = 0
    count = google.count()
    stats["google"] = {
        "count": count,
        "sum": sum,
        "share": creator.google_share,
        "revenue": int(creator.google_share * sum / 100),
    }

    return JsonResponse(status=200, data={
        "total_sells_count": stats["google"]["count"] + stats["myket"]["count"] + stats["bazaar"]["count"] + stats["zarinpal"]["count"],
        "total_sells_amount": stats["google"]["sum"] + stats["myket"]["sum"] + stats["bazaar"]["sum"] + stats["zarinpal"]["sum"],
        "total_revenue": stats["google"]["revenue"] + stats["myket"]["revenue"] + stats["bazaar"]["revenue"] + stats["zarinpal"]["revenue"],
        "stats": stats,
        "is_coin": False,
    })


@api_view(['POST', ])
@permission_classes([])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def coin_creator_cashout(request):
    member = request.user
    data = request.data

    creators = ContentCreator.objects.filter(member=member, enabled=True)
    if len(creators) == 0:
        return JsonResponse(status=400, data={"message": "Not Found"})
    
    creator : ContentCreator = creators[0]

    if creator.is_coin:
        coin_log = AddCoin()
        coin_log.member = member
        coin_log.value = int(creator.coin_sum * creator.coin_share / 100)
        coin_log.type = "CreatorCashout"
        coin_log.secondary = ""
        coin_log.save()

    creator.cashout_now(True)

    return JsonResponse(status=200, data={"message": "Success"})
