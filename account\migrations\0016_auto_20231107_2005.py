# Generated by Django 2.2.28 on 2023-11-07 16:35

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0015_member_requested_email'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='member',
            name='requested_email',
        ),
        migrations.AddField(
            model_name='member',
            name='linked_member',
            field=models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='links', to=settings.AUTH_USER_MODEL),
        ),
    ]
