# Generated by Django 2.2.28 on 2023-11-11 14:48

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0016_auto_20231107_2005'),
    ]

    operations = [
        migrations.CreateModel(
            name='MetaProgressLevel',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('removed', models.DateTimeField(blank=True, default=None, editable=False, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('last_update', models.DateTimeField(auto_now=True)),
                ('level', models.IntegerField(default=1)),
                ('cup', models.IntegerField(default=0)),
                ('game_count', models.IntegerField(default=0)),
                ('prize_type', models.CharField(choices=[('Coin', 'Coin'), ('Character', 'Character')], default='Coin', max_length=100)),
                ('progress_type', models.CharField(choices=[('GameCount', 'GameCount'), ('Cup', 'Cup')], default='Cup', max_length=100)),
                ('next', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='account.MetaProgressLevel')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
