# Generated by Django 2.2.28 on 2023-11-12 18:13

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0020_auto_20231112_2110'),
    ]

    operations = [
        migrations.AddField(
            model_name='member',
            name='progress_level',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='account.MetaProgressLevel'),
        ),
        migrations.AlterField(
            model_name='metaprogresslevel',
            name='progress_type',
            field=models.CharField(choices=[('GameCount', 'GameCount'), ('Cup', 'Cup'), ('WinCount', 'WinCount')], default='Cup', max_length=100),
        ),
    ]
