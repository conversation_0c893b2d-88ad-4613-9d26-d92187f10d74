# Generated by Django 2.2.28 on 2025-02-03 14:57

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0067_auto_20250203_1800'),
    ]

    operations = [
        migrations.AddField(
            model_name='contentcreator',
            name='bazaar_share',
            field=models.IntegerField(default=7),
        ),
        migrations.AddField(
            model_name='contentcreator',
            name='google_share',
            field=models.IntegerField(default=7),
        ),
        migrations.AddField(
            model_name='contentcreator',
            name='myket_share',
            field=models.IntegerField(default=5),
        ),
        migrations.AddField(
            model_name='contentcreator',
            name='zarinpal_share',
            field=models.IntegerField(default=7),
        ),
        migrations.AlterField(
            model_name='member',
            name='market',
            field=models.CharField(choices=[('CafeBazaar', 'CafeBazaar'), ('Myket', 'Myket'), ('<PERSON><PERSON><PERSON><PERSON>', '<PERSON>ari<PERSON><PERSON>'), ('GooglePlay', 'GooglePlay'), ('Desktop', 'Desktop')], default='Myket', max_length=100),
        ),
    ]
