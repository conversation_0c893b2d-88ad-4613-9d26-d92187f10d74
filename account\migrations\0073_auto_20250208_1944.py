# Generated by Django 2.2.28 on 2025-02-08 16:14

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0072_contentcreator_name'),
    ]

    operations = [
        migrations.AddField(
            model_name='contentcreator',
            name='coin_share',
            field=models.IntegerField(default=5),
        ),
        migrations.AddField(
            model_name='contentcreator',
            name='is_coin',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='contentcreator',
            name='last_cashout',
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
    ]
