# Generated by Django 2.2.28 on 2025-06-05 20:48

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0084_member_vehicle_spawn'),
    ]

    operations = [
        migrations.CreateModel(
            name='SecretKey',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('removed', models.DateTimeField(blank=True, default=None, editable=False, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('last_update', models.DateTimeField(auto_now=True)),
                ('key', models.CharField(default='', max_length=100)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='syncdata',
            name='check_secret_key',
            field=models.BooleanField(default=False),
        ),
    ]
