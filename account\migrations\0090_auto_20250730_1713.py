# Generated by Django 2.2.28 on 2025-07-30 13:43

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0089_member_remove_code'),
    ]

    operations = [
        migrations.AddField(
            model_name='member',
            name='season_premium',
            field=models.BooleanField(default=False, help_text='Whether user has purchased premium battle pass for current season'),
        ),
        migrations.AddField(
            model_name='member',
            name='season_smart',
            field=models.IntegerField(default=0, help_text='Current season smart points for battle pass progression'),
        ),
    ]
