# Generated by Django 2.2.28 on 2025-09-03 12:51

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0090_auto_20250730_1713'),
    ]

    operations = [
        migrations.CreateModel(
            name='DailyActiveUsers',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('removed', models.DateTimeField(blank=True, default=None, editable=False, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('last_update', models.DateTimeField(auto_now=True)),
                ('date', models.DateField(help_text='The date for which the active users are counted', unique=True)),
                ('total_active_users', models.IntegerField(default=0, help_text='Total number of unique users who were active on this date')),
                ('new_users', models.IntegerField(default=0, help_text='Number of new users who registered on this date')),
                ('returning_users', models.IntegerField(default=0, help_text='Number of users who were active on this date but registered before')),
                ('users_played_games', models.IntegerField(default=0, help_text='Number of users who played at least one game on this date')),
                ('users_made_purchases', models.IntegerField(default=0, help_text='Number of users who made at least one purchase on this date')),
                ('myket_users', models.IntegerField(default=0, help_text='Number of active users from Myket market')),
                ('googleplay_users', models.IntegerField(default=0, help_text='Number of active users from Google Play market')),
                ('bazaar_users', models.IntegerField(default=0, help_text='Number of active users from Bazaar market')),
                ('desktop_users', models.IntegerField(default=0, help_text='Number of active users from Desktop')),
                ('other_market_users', models.IntegerField(default=0, help_text='Number of active users from other markets')),
            ],
            options={
                'verbose_name': 'Daily Active Users',
                'verbose_name_plural': 'Daily Active Users',
                'ordering': ['-date'],
            },
        ),
    ]
