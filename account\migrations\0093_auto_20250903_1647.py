# Generated by Django 2.2.28 on 2025-09-03 13:17

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('account', '0092_dailyactiveusers_zarinpal_users'),
    ]

    operations = [
        migrations.CreateModel(
            name='DailyGameSession',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('removed', models.DateTimeField(blank=True, default=None, editable=False, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('last_update', models.DateTimeField(auto_now=True)),
                ('date', models.DateField(help_text='The date of the game session')),
                ('device', models.CharField(blank=True, default='', help_text='Device information from member sync', max_length=100)),
                ('game_version', models.IntegerField(default=0, help_text='Game version from member sync')),
                ('market', models.CharField(default='', help_text='Market/platform from member data', max_length=100)),
                ('first_sync_time', models.DateTimeField(auto_now_add=True, help_text='When the member first synced on this date')),
                ('last_sync_time', models.DateTimeField(auto_now=True, help_text='When the member last synced on this date')),
                ('sync_count', models.IntegerField(default=1, help_text='Number of times member synced on this date')),
                ('member', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='daily_sessions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Daily Game Session',
                'verbose_name_plural': 'Daily Game Sessions',
                'ordering': ['-date', 'member'],
            },
        ),
        migrations.AddIndex(
            model_name='dailygamesession',
            index=models.Index(fields=['date'], name='account_dai_date_7997cc_idx'),
        ),
        migrations.AddIndex(
            model_name='dailygamesession',
            index=models.Index(fields=['member', 'date'], name='account_dai_member__e255ac_idx'),
        ),
        migrations.AddIndex(
            model_name='dailygamesession',
            index=models.Index(fields=['market', 'date'], name='account_dai_market_fbfc75_idx'),
        ),
        migrations.AddIndex(
            model_name='dailygamesession',
            index=models.Index(fields=['game_version', 'date'], name='account_dai_game_ve_8021a4_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='dailygamesession',
            unique_together={('member', 'date')},
        ),
    ]
