from django.db import models
from django.utils import timezone
from utils.models import BaseModel


class DailyGameSession(BaseModel):
    """
    Model to track daily game sessions for each member.
    Creates one record per member per day when they sync.
    """
    
    member = models.ForeignKey(
        'account.Member', 
        on_delete=models.CASCADE, 
        related_name='daily_sessions'
    )
    
    date = models.DateField(
        help_text="The date of the game session"
    )
    
    device = models.CharField(
        max_length=100, 
        default="", 
        blank=True,
        help_text="Device information from member sync"
    )
    
    game_version = models.IntegerField(
        default=0,
        help_text="Game version from member sync"
    )
    
    market = models.CharField(
        max_length=100, 
        default="",
        help_text="Market/platform from member data"
    )
    
    # Session tracking fields
    first_sync_time = models.DateTimeField(
        auto_now_add=True,
        help_text="When the member first synced on this date"
    )
    
    last_sync_time = models.DateTimeField(
        auto_now=True,
        help_text="When the member last synced on this date"
    )
    
    sync_count = models.Integer<PERSON>ield(
        default=1,
        help_text="Number of times member synced on this date"
    )
    
    class Meta:
        unique_together = ('member', 'date')
        ordering = ['-date', 'member']
        verbose_name = "Daily Game Session"
        verbose_name_plural = "Daily Game Sessions"
        indexes = [
            models.Index(fields=['date']),
            models.Index(fields=['member', 'date']),
            models.Index(fields=['market', 'date']),
            models.Index(fields=['game_version', 'date']),
        ]
    
    def __str__(self):
        return f"{self.member.handle} - {self.date} ({self.market})"
    
    @property
    def session_duration_minutes(self):
        """Calculate session duration in minutes"""
        if self.first_sync_time and self.last_sync_time:
            delta = self.last_sync_time - self.first_sync_time
            return int(delta.total_seconds() / 60)
        return 0
    
    @property
    def is_active_session(self):
        """Check if this is an active session (last sync within 30 minutes)"""
        if self.last_sync_time:
            time_diff = timezone.now() - self.last_sync_time
            return time_diff.total_seconds() < 1800  # 30 minutes
        return False
