import json, logging
import copy
from datetime import timedelta
from rest_framework_jwt.settings import api_settings
from django.conf import settings
from django.utils import timezone
from django.db import models
from django.utils.translation import ugettext_lazy as _
from django.db.models import Q, Sum

from shop.models import Purchase
from shop.utils import create_freddy_character_own, create_parinaz_character_own
from utils.models import BaseUser, BaseModel
from utils.utils import slug_generator, number_generator, send_email
from .meta_progress import PROGRESS_TYPE_CUP, PROGRESS_TYPE_GAME_COUNT, PROGRESS_TYPE_WIN_COUNT, MetaProgressLevel

jwt_payload_handler = api_settings.JWT_PAYLOAD_HANDLER
jwt_encode_handler = api_settings.JWT_ENCODE_HANDLER
jwt_decode_handler = api_settings.JWT_DECODE_HANDLER

logger = logging.getLogger('django')


MEMBER_DATA_DEFAULT = {
    "selected_character_id": 1,
	"JWT": "",
}

MARKET_BAZAAR = "CafeBazaar"
MARKET_MYKET = "Myket"
MARKET_ZARINPAL = "Zarinpal"
MARKET_GOOGLEPLAY = "GooglePlay"
MARKET_DESKTOP = "Desktop"

MARKET_CHOICES = (
    (MARKET_BAZAAR, MARKET_BAZAAR),
    (MARKET_MYKET, MARKET_MYKET),
    (MARKET_ZARINPAL, MARKET_ZARINPAL),
    (MARKET_GOOGLEPLAY, MARKET_GOOGLEPLAY),
    (MARKET_DESKTOP, MARKET_DESKTOP),
)


PRESENCE_MAIN_MENU = "MainMenu"
PRESENCE_RACE_MODE = "Race"
PRESENCE_FREERIDE_MODE = "Freeride"
PRESENCE_CHOICES = (
    (PRESENCE_MAIN_MENU, PRESENCE_MAIN_MENU),
    (PRESENCE_RACE_MODE, PRESENCE_RACE_MODE),
    (PRESENCE_FREERIDE_MODE, PRESENCE_FREERIDE_MODE),
)


class Member(BaseUser):
    coin = models.IntegerField(default=0)
    handle = models.CharField(max_length=250, default="", blank=True, null=True)
    cup = models.IntegerField(default=0)
    crown = models.IntegerField(default=0)
    smart = models.IntegerField(default=0)
    hunger = models.FloatField(default=100)
    warmth = models.FloatField(default=100)
    title = models.CharField(max_length=100, default="", blank=True)
    title_color = models.CharField(max_length=10, default="", blank=True)
    last_login = models.DateTimeField(default=timezone.now)
    is_test = models.BooleanField(default=False)
    market = models.CharField(max_length=100, choices=MARKET_CHOICES, default=MARKET_MYKET)
    last_daily_claim = models.DateTimeField(null=True, default=None, blank=True)
    tutorial_finished = models.BooleanField(default=False)
    game_version = models.IntegerField(default=0)
    ban_till_time = models.DateTimeField(default=None, blank=True, null=True)
    jail_till_time = models.DateTimeField(default=None, blank=True, null=True)
    in_prison = models.BooleanField(default=False)
    email_code = models.CharField(max_length=50, default="", blank=True, null=True)
    remove_code = models.CharField(max_length=50, default="", blank=True, null=True)

    # PropHunt Season Fields
    season_smart = models.IntegerField(default=0, help_text="Current season smart points for battle pass progression")
    season_premium = models.BooleanField(default=False, help_text="Whether user has purchased premium battle pass for current season")

    linked_member = models.ForeignKey("account.Member", related_name='links', on_delete=models.CASCADE, null=True, default=None)
    game_count = models.IntegerField(default=0)
    win_count = models.IntegerField(default=0)
    first_place_count = models.IntegerField(default=0)
    progress_level = models.ForeignKey('account.MetaProgressLevel', null=True, blank=True, on_delete=models.CASCADE)
    show_debug = models.BooleanField(default=False)
    device = models.CharField(max_length=100, default="", null=True, blank=True)
    chat_admin = models.BooleanField(default=False)
    free_admin = models.BooleanField(default=False)
    bazras = models.BooleanField(default=False)
    elite_bazras = models.BooleanField(default=False)
    name_changed_count = models.IntegerField(default=0)
    friends = models.ManyToManyField('account.Member', through='mobile.Friendship')
    mobile_number  = models.CharField(max_length=100, blank=True, default='')
    presence = models.CharField(max_length=100, blank=True, default=PRESENCE_MAIN_MENU, choices=PRESENCE_CHOICES)
    presence_last_update = models.DateTimeField(blank=True, default=timezone.now)
    presence_data = models.CharField(max_length=100, blank=True, default='')
    log_data = models.TextField(blank=True, default='')
    referer_content_creator = models.ForeignKey('account.ContentCreator', on_delete=models.CASCADE, null=True, blank=True, default=None, related_name="referers")
    stealth = models.BooleanField(default=False)
    force_iap = models.BooleanField(default=False)
    force_ipg = models.BooleanField(default=False)
    switch_to_zarinpal = models.BooleanField(default=False)
    ipg_shop_discount = models.IntegerField(default=0)
    desktop_verified = models.BooleanField(default=False)
    full_ban = models.BooleanField(default=False)
    police_job_enabled = models.BooleanField(default=False)
    hide_in_ranks = models.BooleanField(default=False)
    last_suspicous_email = models.DateTimeField(default=timezone.now)
    vehicle_spawn = models.BooleanField(default=True)
    server_verified = models.BooleanField(default=False)
    
    
    #Debug booleans
    tutorial_started = models.BooleanField(default=False)
    find_game = models.BooleanField(default=False)
    find_game_count = models.IntegerField(default=0)
    nakama_connect = models.BooleanField(default=False)
    server_test_fail = models.BooleanField(default=False)
    
    @property
    def token(self):
        payload = jwt_payload_handler(self)
        payload["username"] = self.username
        payload['exp'] = timezone.now() + settings.JWT_EXPIRATION_DELTA
        token = jwt_encode_handler(payload)
        
        return token
    

    def init(self, market, device):
        self.username = slug_generator(size=10)
        self.handle = "Player#" + number_generator(size=5)
        self.coin = 200
        if market == None:
            market = MARKET_MYKET
        self.market = market
        self.device = device
        self.hunger = 100
        self.warmth = 100
        self.progress_level = MetaProgressLevel.objects.get(level=1)
        self.save()
        data_member = MemberData()
        data_member.member = self
        data = copy.deepcopy(MEMBER_DATA_DEFAULT)
        data["username"] = self.username
        data["handle"] = self.handle
        data["JWT"] = self.token
        data["id"] = self.id
        data["cup"] = 200
        data["coin"] = 0
        data_member.data_string = json.dumps(data)
        data_member.save()
        create_freddy_character_own(self)
        create_parinaz_character_own(self)
    

    @property
    def is_online(self):
        return (timezone.now() - self.presence_last_update).seconds <= 1.5 * 60


    @property
    def nakama(self):
        return self.nakama_connect
    
    
    def add_cup_and_coin(self, cup_value, coin_value):
        member_data = self.data.all()[0]
        json_data = member_data.data
        json_data["cup"] = json_data.get("cup", 0) + cup_value
        json_data["coin"] = json_data.get("coin", 0) + coin_value
        member_data.data_string = json.dumps(json_data)
        member_data.save()
        self.cup += cup_value
        self.coin += coin_value
        self.save()


    @property
    def is_meta_progress_finished(self):
        if self.progress_level is None:
            return False

        if self.progress_level.progress_type == PROGRESS_TYPE_CUP:
            return self.cup >= self.progress_level.cup

        if self.progress_level.progress_type == PROGRESS_TYPE_GAME_COUNT:
            return self.game_count >= self.progress_level.game_count

        if self.progress_level.progress_type == PROGRESS_TYPE_WIN_COUNT:
            return self.win_count >= self.progress_level.game_count

        return False


    def add_ban(self, seconds, remove_title=True):
        if self.ban_till_time is None or self.ban_till_time < timezone.now():
            self.ban_till_time = timezone.now()
        
        if remove_title:
            self.title = ""
        self.ban_till_time += timedelta(seconds=seconds)
        self.save()


    @property
    def is_ban(self):
        if self.ban_till_time is None or self.ban_till_time < timezone.now():
            return False
        
        return True


    @property
    def ban_time(self):
        if not self.is_ban:
            return 0

        return (self.ban_till_time - timezone.now()).total_seconds()
    

    def add_jail(self, seconds):
        if self.jail_till_time is None or self.jail_till_time < timezone.now():
            self.jail_till_time = timezone.now()
        
        #self.title = ""
        self.jail_till_time += timedelta(seconds=seconds)
        self.save()


    @property
    def is_jail(self):
        if self.jail_till_time is None or self.jail_till_time < timezone.now():
            return False
        
        return True


    @property
    def jail_time(self):
        if not self.is_jail:
            return 0

        return (self.jail_till_time - timezone.now()).total_seconds()


    @property
    def daily_claim_time(self):
        if self.last_daily_claim:
            return int(self.last_daily_claim.timestamp())
        return 0


    def __str__(self):
        return "(" + str(self.id) + ") " + self.username + " "# + self.handle


    @property
    def ban_from_chat(self):
        return False #Handled elsewhere


    @property
    def clan(self):
        membership = self.clanmembership.filter(state="Accepted")
        if len(membership) == 0:
            return None
        return membership[0].clan


    @property
    def clan_membership(self):
        membership = self.clanmembership.filter(state="Accepted")
        if len(membership) == 0:
            return None
        return membership[0]


    def fraud_kill(self, delete_characters_time=0):
        self.coin = 0
        self.hunger  = 0
        self.title = ""
        member_data = self.data.all()[0]
        data = json.loads(member_data.data_string)
        data["selected_character_id"] = 1
        member_data.data_string = json.dumps(data)
        member_data.save()
        
        if delete_characters_time == -1:
            self.characters.all().filter(~Q(character__id=1)).delete()
        else:
            unlock_time = timezone.now() - delete_characters_time
            self.characters.filter(created__gt=unlock_time).filter(~Q(character__id=1)).delete()
        self.save()


class MemberData(BaseModel):
    member = models.ForeignKey(Member, on_delete=models.CASCADE, related_name='data')
    data_string = models.TextField(default='{}', blank=True)

    @property
    def data(self):
        ret = json.loads(self.data_string)
        ret["inventory"] = ret.get("inventory", {})
        try:
            ret["inventory"]["inventory"] = ret["inventory"].get("inventory", [])
        except:
            ret["inventory"] = {}
            ret["inventory"]["inventory"] = ret["inventory"].get("inventory", [])

        while len(ret["inventory"]["inventory"]) < 3:
            ret["inventory"]["inventory"].append(None)

        return ret


class ContentCreator(BaseModel):
    name = models.CharField(max_length=100, default="", blank=True)
    member = models.ForeignKey(Member, on_delete=models.CASCADE)
    code = models.CharField(max_length=50, default='', blank=True)
    myket_share = models.IntegerField(default=3)
    bazaar_share = models.IntegerField(default=5)
    zarinpal_share = models.IntegerField(default=5)
    google_share = models.IntegerField(default=5)
    coin_share = models.IntegerField(default=5)
    enabled = models.BooleanField(default=True)
    is_coin = models.BooleanField(default=True)
    last_cashout = models.DateTimeField(default=timezone.now)


    def __str__(self):
        return self.name + " (" + self.code + ")"
    

    def cashout_now(self, update_time=True):

        if self.is_coin:
            coins = int(self.coin_sum * self.coin_share / 100)
            self.member.coin += coins
            self.member.save()
        else:
            myket = self.calculate_gateway_income(MARKET_MYKET)
            bazaar = self.calculate_gateway_income(MARKET_BAZAAR)
            zarinpal = self.calculate_gateway_income(MARKET_ZARINPAL)
            google = self.calculate_gateway_income(MARKET_GOOGLEPLAY)

            total_sells_count = myket["count"] + bazaar["count"] + zarinpal["count"] + google["count"]
            total_sells_amount = myket["sum"] + bazaar["sum"] + zarinpal["sum"] + google["sum"]
            total_revenue = int(myket["sum"] * self.myket_share / 100) + int(bazaar["sum"] * self.bazaar_share / 100) + int(zarinpal["sum"] * self.zarinpal_share / 100) + int(google["sum"] * self.google_share / 100)
            
            logger.info("Member: {}\nSales Count: {}\nSales Amount: {}\nRevenue: {}\n"
                         .format(str(self.member) + " " + self.member.handle, total_sells_count, total_sells_amount, total_revenue))
            send_email.delay("<EMAIL>", "Creator Revenue Report {}".format(self.name), "Member: {}\nSales Count: {}\nSales Amount: {}\nRevenue: {}\n"
                         .format(str(self.member) + " " + self.member.handle, total_sells_count, total_sells_amount, total_revenue))
        

        if update_time:
            self.last_cashout = timezone.now()
        self.save()

    

    @property
    def number_of_followers(self):
        return Member.objects.filter(referer_content_creator=self).count()


    @property
    def total_purchase_count(self):
        return Purchase.objects.filter(is_test=False, referer_content_creator=self).count()


    @property
    def purchase_count(self):
        return Purchase.objects.filter(is_test=False, referer_content_creator=self, created__gt=self.last_cashout).count()


    @property
    def total_purchase_sum(self):
        sum = Purchase.objects.filter(is_test=False, referer_content_creator=self).aggregate(sum=Sum('item__price_tag'))["sum"]
        if sum == None:
            sum = 0
        zar_sum = Purchase.objects.filter(is_test=False, referer_content_creator=self).filter(market=MARKET_ZARINPAL).aggregate(sum=Sum('paid_price'))["sum"]
        if zar_sum == None:
            zar_sum = 0

        return sum  + zar_sum


    @property
    def purchase_sum(self):
        sum = Purchase.objects.filter(is_test=False, referer_content_creator=self, created__gt=self.last_cashout).filter(~Q(market=MARKET_ZARINPAL)).aggregate(sum=Sum('item__price_tag'))["sum"]
        if sum == None:
            sum = 0
        
        zar_sum = Purchase.objects.filter(is_test=False, referer_content_creator=self, created__gt=self.last_cashout).filter(market=MARKET_ZARINPAL).aggregate(sum=Sum('paid_price'))["sum"]
        if zar_sum == None:
            zar_sum = 0

        return sum + zar_sum
    

    @property
    def purchase_sum_(self):
        return "{:,}".format(self.purchase_sum)
    

    @property
    def coin_sum(self):
        lt = timezone.now() - timedelta(minutes=15)
        sum = Purchase.objects.filter(is_test=False, referer_content_creator=self, created__gt=self.last_cashout).filter(created__lt=lt).aggregate(sum=Sum('item__coins'))["sum"]
        if sum == None:
            return 0

        return sum
    

    @property
    def coin_revenue(self):
        if self.is_coin:
            return int(self.coin_sum * self.coin_share / 100)
        return 0
    

    def calculate_gateway_income(self, gateway):
        purchases = Purchase.objects.filter(is_test=False, referer_content_creator=self, market=gateway, created__gt=self.last_cashout)
        if gateway == MARKET_ZARINPAL:
            sum = purchases.aggregate(sum=Sum('paid_price'))["sum"]
        else:
            sum = purchases.aggregate(sum=Sum('item__price_tag'))["sum"]
        if sum == None:
            sum = 0
        count = purchases.count()
        return {
            "count": count,
            "sum": sum,
        }
