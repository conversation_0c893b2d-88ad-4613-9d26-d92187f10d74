from datetime import datetime, timedelta
from django.utils import timezone
from django.db import models

from utils.models import BaseModel


PRIZE_TYPE_COIN = "Coin"
PRIZE_TYPE_CHARACTER = "Character"
PRIZE_CHOICES = (
    (PRIZE_TYPE_COIN, PRIZE_TYPE_COIN),
    (PRIZE_TYPE_CHARACTER, PRIZE_TYPE_CHARACTER),
)

PROGRESS_TYPE_WIN_COUNT = "WinCount"
PROGRESS_TYPE_GAME_COUNT = "GameCount"
PROGRESS_TYPE_CUP = "Cup"
PROGRESS_TYPE = (
    (PROGRESS_TYPE_GAME_COUNT, PROGRESS_TYPE_GAME_COUNT),
    (PROGRESS_TYPE_CUP, PROGRESS_TYPE_CUP),
    (PROGRESS_TYPE_WIN_COUNT, PROGRESS_TYPE_WIN_COUNT),
)

class MetaProgressLevel(BaseModel):
    level = models.IntegerField(default=1)
    progress_type = models.CharField(max_length=100, choices=PROGRESS_TYPE, default=PROGRESS_TYPE_CUP)
    cup = models.IntegerField(default=0)
    game_count = models.IntegerField(default=0)# Use both for game count and win count
    prize_type = models.CharField(max_length=100, choices=PRIZE_CHOICES, default=PRIZE_TYPE_COIN)
    prize_coin = models.IntegerField(default=0)
    prize_character = models.ForeignKey('shop.Character', null=True, blank=True, on_delete=models.CASCADE)
    next = models.ForeignKey('account.MetaProgressLevel', null=True, blank=True, on_delete=models.CASCADE)

    def __str__(self) -> str:
        return "Level " + str(self.level)
