from datetime import datetime, timedelta
from django.utils import timezone
from django.db import models

from utils.models import BaseModel

class SyncData(BaseModel):
    unlock_character_coin = models.IntegerField(default=0)
    character_common = models.IntegerField(default=0)
    character_rare = models.IntegerField(default=0)
    character_legendary = models.IntegerField(default=0)
    character_epic = models.IntegerField(default=0)
    character_common_exact = models.IntegerField(default=0)
    character_rare_exact = models.IntegerField(default=0)
    character_legendary_exact = models.IntegerField(default=0)
    character_epic_exact = models.IntegerField(default=0)
    character_special_exact = models.IntegerField(default=1000)
    game_tax = models.IntegerField(default=0)
    game_is_down = models.BooleanField(default=False)
    up_datetime = models.DateTimeField(default=timezone.now)
    last_version = models.IntegerField(default=1)
    force_version = models.IntegerField(default=1)
    chat_cup = models.IntegerField(default=0)
    change_name_price = models.IntegerField(default=0)
    animation_price = models.IntegerField(default=0)
    coin_transfer_fee = models.IntegerField(default=10)
    day_start_hour = models.IntegerField(default=6)
    night_start_hour = models.IntegerField(default=6)
    arrest_time = models.IntegerField(default=120)
    reward_video_coins = models.IntegerField(default=8)
    reward_video_hp = models.IntegerField(default=8)
    max_friends_count = models.IntegerField(default=1)
    force_update_from_url = models.BooleanField(default=False)
    update_store_url = models.CharField(max_length=300, default="", blank=True)
    public_chat_channels = models.IntegerField(default=1)
    voice_call_price = models.IntegerField(default=50)
    voice_call_per_minute = models.IntegerField(default=50)
    shop_check_ip = models.BooleanField(default=False)
    ipg_shop_discount = models.IntegerField(default=10)
    check_secret_key = models.BooleanField(default=False)


    @property
    def up_counter(self):
        return (self.up_datetime - timezone.now()).seconds


class SiteElement(BaseModel):
    name = models.CharField(max_length=100, blank=True, default='')
    element = models.TextField(blank=True, default="")
    order = models.IntegerField(default=1)
    visible = models.BooleanField(default=True)


class SecretKey(BaseModel):
    key = models.CharField(default="", max_length=100)
    