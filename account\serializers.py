from rest_framework import serializers
from .models.member import Member, MemberData
from .models.sync_data import SyncData, SiteElement
from .models.meta_progress import MetaProgressLevel
from shop.serializers import CharacterSerializer

class MetaProgressSerializer(serializers.ModelSerializer):
    prize_character = CharacterSerializer(read_only=True)

    class Meta:
        model = MetaProgressLevel
        fields = ("id", "level", "progress_type", "cup", "game_count", "prize_type", "prize_coin", "prize_character", "next_id")



class MemberSerializer(serializers.ModelSerializer):
    progress_level = MetaProgressSerializer(read_only=True)

    class Meta:
        model = Member
        fields = ("id", "username", "handle", "token", "coin", "cup", "crown", "smart", "is_test", "ban_time", "hunger", "warmth", "title", "title_color",
                   "free_admin", "bazras", "elite_bazras", "ban_from_chat", "email", "game_count", "win_count", "is_meta_progress_finished",
                     "progress_level", "show_debug", "chat_admin", "name_changed_count", "daily_claim_time", "jail_time", "in_prison", "stealth")


class MemberLeaderboardSerializer(serializers.ModelSerializer):
    class Meta:
        model = Member
        fields = ("id", "handle", "cup", "crown", "smart", "win_count", "first_place_count", "coin", "title")


class MemberDataSerializer(serializers.ModelSerializer):
    class Meta:
        model = MemberData
        fields = ("id", "data")


class SiteElementSerializer(serializers.ModelSerializer):
    class Meta:
        model = SiteElement
        fields = ("id", "element", "visible")


class MemberBanSerializer(serializers.ModelSerializer):
    class Meta:
        model = Member
        fields = ("id", "ban_time", "coin", "title", "title_color", "jail_time", "in_prison")


class MemberTransferSerializer(serializers.ModelSerializer):
    class Meta:
        model = Member
        fields = ("id", "username", "handle")


class SyncDataSerializer(serializers.ModelSerializer):
    class Meta:
        model = SyncData
        fields = ("game_is_down", "up_counter", "last_version", "force_version", "unlock_character_coin", "game_tax", "coin_transfer_fee",
                   "chat_cup", "change_name_price", "character_common", "character_rare", "character_epic", "character_legendary",
                     "animation_price", "character_common_exact", "character_rare_exact", "character_epic_exact", "character_legendary_exact",
                     "day_start_hour", "night_start_hour", "reward_video_coins", "reward_video_hp", "force_update_from_url", "update_store_url",
                       "voice_call_per_minute", "voice_call_price", "character_special_exact")


class MemberCharacterLeaderboardSerializer(serializers.ModelSerializer):
    class Meta:
        model = Member
        fields = ("id", "handle", "num_chars", "title")


class MemberFriendshipSerializer(serializers.ModelSerializer):
    class Meta:
        model = Member
        fields = ("id", "username", "handle", "phone_number", "cup")
