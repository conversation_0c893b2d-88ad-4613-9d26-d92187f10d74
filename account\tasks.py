from game_backend.celery import app
import logging
from datetime import date, timedelta
from django.utils import timezone
from django.db.models import Count, Q

from .models import Member, DailyActiveUsers
from .models.member import MARKET_MYKET, MARKET_GOOGLEPLAY, MARKET_BAZAAR, MARKET_DESKTOP, MARKET_ZARINPAL
from game.models import GameJoined
from shop.models import Purchase

logger = logging.getLogger('django')


@app.task
def calculate_daily_active_users(target_date=None):
    """
    Calculate and store daily active users statistics for a given date.
    If no date is provided, calculates for yesterday.
    
    Args:
        target_date (str): Date in YYYY-MM-DD format. If None, uses yesterday.
    
    Returns:
        dict: Summary of calculated statistics
    """
    if target_date:
        # Parse the provided date string
        from datetime import datetime
        calc_date = datetime.strptime(target_date, '%Y-%m-%d').date()
    else:
        # Use yesterday as default
        calc_date = date.today() - timedelta(days=1)
    
    logger.info(f"Calculating daily active users for {calc_date}")
    
    # Define the date range for the calculation
    start_datetime = timezone.make_aware(timezone.datetime.combine(calc_date, timezone.datetime.min.time()))
    end_datetime = start_datetime + timedelta(days=1)
    
    # Get or create the DailyActiveUsers record for this date
    dau_record, created = DailyActiveUsers.objects.get_or_create(
        date=calc_date,
        defaults={
            'total_active_users': 0,
            'new_users': 0,
            'returning_users': 0,
            'users_played_games': 0,
            'users_made_purchases': 0,
            'myket_users': 0,
            'googleplay_users': 0,
            'bazaar_users': 0,
            'desktop_users': 0,
            'zarinpal_users': 0,
            'other_market_users': 0,
        }
    )
    
    # Calculate active users based on last_login or presence_last_update
    active_users = Member.objects.filter(
        Q(last_login__gte=start_datetime, last_login__lt=end_datetime) |
        Q(presence_last_update__gte=start_datetime, presence_last_update__lt=end_datetime)
    ).exclude(is_test=True).distinct()
    
    total_active_users = active_users.count()
    
    # Calculate new users (registered on this date)
    new_users = active_users.filter(
        date_joined__gte=start_datetime,
        date_joined__lt=end_datetime
    ).count()
    
    # Calculate returning users
    returning_users = total_active_users - new_users
    
    # Calculate users who played games
    users_played_games = active_users.filter(
        players__game__created__gte=start_datetime,
        players__game__created__lt=end_datetime,
        players__is_bot=False
    ).distinct().count()
    
    # Calculate users who made purchases
    # Get purchase IDs for the date range that are not test purchases
    purchase_member_ids = Purchase.objects.filter(
        created__gte=start_datetime,
        created__lt=end_datetime,
        is_test=False
    ).values_list('member_id', flat=True).distinct()

    users_made_purchases = active_users.filter(id__in=purchase_member_ids).count()
    
    # Calculate market breakdown
    market_breakdown = active_users.values('market').annotate(count=Count('id'))
    market_counts = {item['market']: item['count'] for item in market_breakdown}
    
    # Map market values to field names
    myket_users = market_counts.get(MARKET_MYKET, 0)
    googleplay_users = market_counts.get(MARKET_GOOGLEPLAY, 0)
    bazaar_users = market_counts.get(MARKET_BAZAAR, 0)
    desktop_users = market_counts.get(MARKET_DESKTOP, 0)
    zarinpal_users = market_counts.get(MARKET_ZARINPAL, 0)

    # Calculate other markets
    other_market_users = total_active_users - (myket_users + googleplay_users + bazaar_users + desktop_users + zarinpal_users)
    
    # Update the record
    dau_record.total_active_users = total_active_users
    dau_record.new_users = new_users
    dau_record.returning_users = returning_users
    dau_record.users_played_games = users_played_games
    dau_record.users_made_purchases = users_made_purchases
    dau_record.myket_users = myket_users
    dau_record.googleplay_users = googleplay_users
    dau_record.bazaar_users = bazaar_users
    dau_record.desktop_users = desktop_users
    dau_record.zarinpal_users = zarinpal_users
    dau_record.other_market_users = other_market_users
    dau_record.save()
    
    # Prepare summary
    summary = {
        'date': str(calc_date),
        'total_active_users': total_active_users,
        'new_users': new_users,
        'returning_users': returning_users,
        'users_played_games': users_played_games,
        'users_made_purchases': users_made_purchases,
        'retention_rate': dau_record.retention_rate,
        'gaming_engagement_rate': dau_record.gaming_engagement_rate,
        'purchase_conversion_rate': dau_record.purchase_conversion_rate,
        'market_breakdown': {
            'myket': myket_users,
            'googleplay': googleplay_users,
            'bazaar': bazaar_users,
            'desktop': desktop_users,
            'zarinpal': zarinpal_users,
            'other': other_market_users
        },
        'created': created
    }
    
    logger.info(f"Daily active users calculation completed for {calc_date}: {summary}")
    return summary


@app.task
def calculate_weekly_dau_batch():
    """
    Calculate DAU for the last 7 days in batch.
    Useful for backfilling data or weekly reports.
    """
    results = []
    for i in range(7):
        target_date = date.today() - timedelta(days=i+1)
        result = calculate_daily_active_users(target_date.strftime('%Y-%m-%d'))
        results.append(result)
    
    logger.info(f"Weekly DAU batch calculation completed for {len(results)} days")
    return results


@app.task
def cleanup_old_dau_records(days_to_keep=365):
    """
    Clean up old DAU records to prevent database bloat.
    Keeps records for the specified number of days (default: 1 year).
    
    Args:
        days_to_keep (int): Number of days of records to keep
    
    Returns:
        int: Number of records deleted
    """
    cutoff_date = date.today() - timedelta(days=days_to_keep)
    deleted_count, _ = DailyActiveUsers.objects.filter(date__lt=cutoff_date).delete()
    
    logger.info(f"Cleaned up {deleted_count} old DAU records older than {cutoff_date}")
    return deleted_count
