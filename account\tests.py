from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase
from rest_framework import status
from unittest.mock import patch
import json

from .models.member import Member


class EmailRemovalAPITestCase(APITestCase):
    def setUp(self):
        """Set up test data"""
        self.member = Member.objects.create(
            username="testuser",
            handle="TestPlayer",
            email="<EMAIL>",
            coin=200
        )

        # Authenticate the member
        self.client.force_authenticate(user=self.member)

    @patch('account.function_views.send_email.delay')
    def test_request_remove_email_success(self, mock_send_email):
        """Test successful email removal request"""
        url = reverse('request_remove_email')
        response = self.client.post(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()['message'], "Remove code sent to your email")

        # Refresh member from database
        self.member.refresh_from_db()

        # Check that remove_code was generated
        self.assertIsNotNone(self.member.remove_code)
        self.assertEqual(len(self.member.remove_code), 6)

        # Check that email was sent
        mock_send_email.assert_called_once()

        # Check log entry
        self.assertIn("email_remove_request: <EMAIL>", self.member.log_data)

    def test_request_remove_email_no_email(self):
        """Test email removal request when no email exists"""
        self.member.email = ""
        self.member.save()

        url = reverse('request_remove_email')
        response = self.client.post(url)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.json()['message'], "No email address found to remove")

    def test_verify_remove_email_success(self):
        """Test successful email removal verification"""
        # Set up remove code
        self.member.remove_code = "123456"
        self.member.save()

        url = reverse('verify_remove_email')
        data = {'code': '123456'}
        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()['message'], "Email removed successfully")

        # Refresh member from database
        self.member.refresh_from_db()

        # Check that email was removed
        self.assertEqual(self.member.email, "")
        self.assertEqual(self.member.remove_code, "")

        # Check log entry
        self.assertIn("email_remove_verify_success: <EMAIL>", self.member.log_data)

    def test_verify_remove_email_invalid_code(self):
        """Test email removal verification with invalid code"""
        self.member.remove_code = "123456"
        self.member.save()

        url = reverse('verify_remove_email')
        data = {'code': '654321'}  # Wrong code
        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.json()['message'], "Invalid remove code")

        # Refresh member from database
        self.member.refresh_from_db()

        # Check that email was NOT removed
        self.assertEqual(self.member.email, "<EMAIL>")

        # Check log entry
        self.assertIn("email_remove_verify_failed: invalid_code=654321", self.member.log_data)

    def test_verify_remove_email_no_email(self):
        """Test email removal verification when no email exists"""
        self.member.email = ""
        self.member.save()

        url = reverse('verify_remove_email')
        data = {'code': '123456'}
        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.json()['message'], "No email address found to remove")

        # Check log entry
        self.member.refresh_from_db()
        self.assertIn("email_remove_verify_failed: no_email", self.member.log_data)
