from django.urls import path
from .function_views import register_member, sync_member, save_data, tutorial_finished, connect_account_with_email, verify_account_code, claim_progress_prize
from .function_views import tutorial_started, nakama_connect, server_test_fail, change_handle, am_i_ban, update_presence, remove_email
from .function_views import request_remove_email, verify_remove_email
from .function_views import list_site_elements, content_creator_stats, refer_content_creator, ami_content_creator, coin_creator_cashout

urlpatterns = [
    path(r'register/', register_member, name="register_member"),
    path(r'sync/', sync_member, name="sync_member"),
    path(r'save_data/', save_data, name="save_data"),
    path(r'change_handle/', change_handle, name="change_handle"),
    path(r'tutorial_finished/', tutorial_finished, name="tutorial_finished"),
    path(r'tutorial_started/', tutorial_started, name="tutorial_started"),
    path(r'nakama_connect/', nakama_connect, name="nakama_connect"),
    path(r'server_test_fail/', server_test_fail, name="server_test_fail"),
    path(r'claim_progress_prize/', claim_progress_prize, name="claim_progress_prize"),
    path(r'am_i_ban/', am_i_ban, name="am_i_ban"),
    path(r'update_presence/', update_presence, name="update_presence"),

    #Account Link
    path(r'request_code/', connect_account_with_email, name="request_code"),
    path(r'verify_code/', verify_account_code, name="verify_code"),
    # path(r'remove_email/', remove_email, name="remove_email"),
    path(r'request_remove_email/', request_remove_email, name="request_remove_email"),
    path(r'verify_remove_email/', verify_remove_email, name="verify_remove_email"),

    #Site
    path(r'list_site_elements/', list_site_elements, name="list_site_elements"),

    #Content Creators
    path(r'ami_content_creator/', ami_content_creator, name="ami_content_creator"),
    path(r'refer_content_creator/', refer_content_creator, name="refer_content_creator"),
    path(r'content_creator_stats/', content_creator_stats, name="content_creator_stats"),
    path(r'coin_creator_cashout/', coin_creator_cashout, name="coin_creator_cashout"),
]
