import random
import json
import logging
import subprocess
from rest_framework import permissions
from rest_framework.decorators import api_view, permission_classes, renderer_classes
from rest_framework_swagger import renderers
from django.http.response import JsonResponse
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.core.validators import validate_email
from django.conf import settings

WSS_URL = "wss://livekit.sizakgames.ir"
API_KEY = "APIHSd8dziPKxZX"
API_SECRET = "H95IFAZMBMDOV7pblejd41gihNteYwfLfvwpgcg5VmhC"

logger = logging.getLogger('django')

@api_view(['POST', ])
@permission_classes([])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def access_token(request):
    member = request.user
    data = request.data

    room_name = data.get("room_name", None)
    if room_name == None:
        return JsonResponse(status=400, data={"message": "Room name not provided"})
    
    #logger.info("request call Start**** ")
    #logger.info(member.username)
    #logger.info("room: " + room_name)
    #logger.info("************")
    
    path = settings.BASE_DIR + "/audio_stream/scripts/livekit-access-token.bash \"%s\" \"%s\" \"%s\"" % (member.username, member.handle, room_name)
    proc = subprocess.run(path, capture_output=True, shell=True)
    logger.info(proc.stderr)
    logger.info(proc.stdout)
    if proc.returncode == 0:
        res = str(proc.stdout)
        #logger.info(res)
        token = res.replace("\\n", '').split("Access token")[1][2:-1]
        return JsonResponse(status=200, data={"token": token})
    
    return JsonResponse(status=401, data={"message": "livekit bash error"})


@api_view(['POST', ])
@permission_classes([])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def end_room(request):
    member = request.user
    data = request.data


    room_name = data.get("room_name", None)
    if room_name == None:
        return JsonResponse(status=400, data={"message": "Room name not provided"})
    
    path = settings.BASE_DIR + "/audio_stream/scripts/livekit-end-room.bash %s" % (room_name)
    proc = subprocess.run(path, capture_output=True, shell=True)
    if proc.returncode == 0:
        return JsonResponse(status=200, data={"message": "success"})
    
    return JsonResponse(status=401, data={"message": "livekit bash error"})


@api_view(['POST', ])
@permission_classes([])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def kick_participant(request):
    member = request.user
    data = request.data

    room_name = data.get("room_name", None)
    if room_name == None:
        return JsonResponse(status=400, data={"message": "Room name not provided"})
    
    particiapnt = data.get("participant", "")
    
    path = settings.BASE_DIR + "/audio_stream/scripts/livekit-kick-participant.bash %s %s" % (room_name, particiapnt)
    #logger.info(path)
    proc = subprocess.run(path, capture_output=True, shell=True)
    if proc.returncode == 0:
        return JsonResponse(status=200, data={"message": "success"})
    
    return JsonResponse(status=401, data={"message": "livekit bash error"})
