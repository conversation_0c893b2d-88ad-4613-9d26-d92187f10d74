# Generated by Django 2.2.28 on 2025-02-03 12:05

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='LiveKitLog',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('removed', models.DateTimeField(blank=True, default=None, editable=False, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('last_update', models.DateTimeField(auto_now=True)),
                ('number_of_rooms', models.IntegerField(default=0)),
                ('total_participants', models.IntegerField(default=0)),
                ('max_room_participant', models.IntegerField(default=0)),
                ('max_room_name', models.Char<PERSON>ield(blank=True, default='', max_length=100)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
