from game_backend.celery import app
import json
import subprocess
import logging
from django.conf import settings

from .models import LiveKitLog
from utils.utils import send_email

logger = logging.getLogger('django')


@app.task
def livekit_room_count():
    path = settings.BASE_DIR + "/audio_stream/scripts/livekit-room-count.bash"
    proc = subprocess.run(path, capture_output=True, shell=True)    
    log = LiveKitLog()
    if proc.returncode == 0:
        res = proc.stdout.decode('utf8').replace("'", '"')

        res = json.loads(res)
        if res.get("rooms", None) == None:
            log.number_of_rooms = 0
            log.total_participants = 0
            log.max_room_participant = 0
        else:
            rooms = res["rooms"]
            log.number_of_rooms = len(rooms)
            total_participants = 0
            max_participant = -1
            for r in rooms:
                par = r.get("num_participants", 1)
                total_participants += par
                if max_participant < par:
                    max_participant = par
                    log.max_room_name = r["name"]
            
            log.max_room_participant = max_participant
            log.total_participants = total_participants
    else:
        log.number_of_rooms = -1
    
    log.save()

