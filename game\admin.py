import json
from django.contrib import admin
from utils.admin import BaseAdmin
from account.models.member import Member
from .models import GameServer, Map, Game, GameJoined, GameSettings, MessageReport, RequestError, GameContainer, MapStage, ContactUsMessage, PurchaseWithCoin
from .models import AddCoin, OnlineStatistics, CoinTransfer, JobDone, Redeem, GiftCode, BazrasReport
from utils.utils import send_email


def remove_owner(modeladmin, request, queryset):
    for server in queryset:
        server.owner = None
        server.save()
remove_owner.short_description = "Remove owner"


def make_public(modeladmin, request, queryset):
    for server in queryset:
        server.has_password = False
        server.password = ""
        server.owner = None
        server.save()
make_public.short_description = "Make server public and remove owner"


def untrust(modeladmin, 
request, queryset):
    queryset.update(trusted=False)
untrust.short_description = "Make servers untrusted"


def trust(modeladmin, request, queryset):
    queryset.update(trusted=True)
trust.short_description = "Make servers trusted"



class GameServerAdmin(BaseAdmin):
    list_display = ('id', 'port', 'mode', 'state', 'name', 'players_joined', 'is_test', 'hide', 'has_password', 'password', 'is_private', 'max_players_start', 'gun_allow', 'max_vehicle', 'is_alive', 'last_update', 'trusted', 'server_key', 'current_stage')
    list_editable = ('is_test', 'hide', 'max_players_start', 'name', 'has_password', 'is_private', 'trusted')
    list_filter = ('mode', 'is_alive', 'is_test', 'has_password', 'is_private', 'ip')
    raw_id_fields = ('owner',)
    actions = [remove_owner, make_public, untrust, trust]


class MapAdmin(BaseAdmin):
    list_display = ('id', 'name', 'type', 'bot_count', 'path', 'difficulty', 'is_active', 'is_test', 'beta', 'weight')
    list_editable = ('is_active', 'is_test', 'beta', 'weight', 'type')
    list_filter = ('type', 'is_active', 'is_test', 'difficulty', 'created')


class MapStageAdmin(BaseAdmin):
    list_display = ('id', 'stage')
    list_editable = ()
    list_filter = ()
    filter_horizontal = ('maps',)


class GameSettingsAdmin(BaseAdmin):
    list_display = ('id', 'lobby_time', 'game_time', 'player_join_add_time')
    list_editable = ()
    list_filter = ()


class GameAdmin(BaseAdmin):
    list_display = ('id', 'created', 'stage', 'total_stage', 'game_server', 'finished', 'is_finished')
    list_editable = ()
    list_filter = ('is_finished', 'finished', 'created')


class GameJoinedAdmin(BaseAdmin):
    list_display = ('id', 'created', 'stage', 'map', 'character', 'handle', 'finished', 'rank', 'time', 'cup', 'coin', "crown", 'game_server', '_fps', '_ping', 'fps_count', 'ping_count', 'is_bot', 'container_id')
    list_editable = ()
    search_fields = ('member__username', 'member__id')
    list_filter = ('finished', 'is_bot', 'map', 'created')
    readonly_fields = ['member', 'game']


class GameContainerAdmin(BaseAdmin):
    list_display = ('id', 'created', 'game_server')
    list_editable = ()
    list_filter = ()


def make_handled(modeladmin, request, queryset):
    queryset.update(handled=True)
make_handled.short_description = "Make Selected messages handled"


def ban_users_time(queryset, seconds):
    for msg in queryset:
        content = json.loads(msg.content)
        if content.get("id", None) != None:
            member = Member.objects.get(username=content["id"])
            member.add_ban(seconds, False)
    queryset.update(handled=True)


def ban_users2h(modeladmin, request, queryset):
    ban_users_time(queryset, seconds=3600 * 2)
ban_users2h.short_description = "2h Ban Selected Users From Chat"


def ban_users4h(modeladmin, request, queryset):
    ban_users_time(queryset, seconds=3600 * 4)
ban_users4h.short_description = "4h Ban Selected Users From Chat"


def ban_users8h(modeladmin, request, queryset):
    ban_users_time(queryset, seconds=3600 * 8)
ban_users8h.short_description = "8h Ban Selected Users From Chat"


def ban_users24h(modeladmin, request, queryset):
    ban_users_time(queryset, seconds=3600 * 24)
ban_users24h.short_description = "24h Ban Selected Users From Chat"


def ban_users7d(modeladmin, request, queryset):
    ban_users_time(queryset, seconds=3600 * 24 * 7)
ban_users7d.short_description = "7d Ban Selected Users From Chat"


class MessageReportAdmin(BaseAdmin):
    list_display = ('id', 'created', 'message_txt', "handle", "username", 'report_handle', 'content', 'member')
    list_editable = ()
    search_fields = ('username', 'member__id', 'member__username', 'member__handle')
    list_filter = ('handled', 'created',)
    actions = [ban_users2h, ban_users4h, ban_users8h, ban_users24h, ban_users7d, make_handled]


class RequestErrorAdmin(BaseAdmin):
    list_display = ('id', 'content')
    list_editable = ()
    list_filter = ('created',)


class ContactUsMessageAdmin(BaseAdmin):
    list_display = ('id', 'created', 'message', 'handle', 'member')
    list_editable = ()
    list_filter = ('created',)
    readonly_fields = ['member', 'message']


class PurchaseWithCoinAdmin(BaseAdmin):
    list_display = ('id', 'created', 'type', 'price', 'secondary', 'success', 'member', 'forgiving')
    list_editable = ('success',)
    list_filter = ('created', 'type')
    search_fields = ('member__id', 'member__handle', 'member__username')
    readonly_fields = ['member',]


class AddCoinAdmin(BaseAdmin):
    list_display = ('id', 'created', 'handle', 'type', 'value', 'secondary', 'member')
    list_editable = ()
    list_filter = ('created', 'type')
    search_fields = ('member__id', 'member__handle', 'member__username')
    readonly_fields = ['member',]


class OnlineStatisticsAdmin(BaseAdmin):
    list_display = ('id', 'created', 'total_online_players', 'number_of_freeride_users', 'number_of_race_users', 'free_race_servers', 'max_freeride_server_user', 'max_race_server_user', 'number_of_freeride_servers', 'number_of_race_servers')
    list_editable = ()
    list_filter = ('created',)


def revert_transfer(modeladmin, request, queryset):
    mail_body = ""
    transaction: CoinTransfer
    for transaction in queryset:
        member: Member = transaction.to_member
        mail_body += member.username + ": " + str(transaction.amount) + ": before=" + str(member.coin)
        member.coin -= transaction.amount
        if member.coin < 0:
            member.coin = 0
        mail_body += " after=" + str(member.coin) + "\n"
        member.save()
    
    send_email.delay("<EMAIL>", "Revert transactions", mail_body)

revert_transfer.short_description = "Revert Transfer"

class CoinTransferAdmin(BaseAdmin):
    list_display = ('id', 'created', 'from_handle', 'from_username', 'to_handle', 'to_username', 'amount', 'from_coin_start', 'from_coin_end', 'to_coin_start', 'to_coin_end', 'fee', 'success')
    list_editable = ()
    list_filter = ('created', 'success')
    search_fields = ('from_member__id', 'to_member__id', 'from_member__handle', 'to_member__handle', 'from_member__username', 'to_member__username')
    readonly_fields = ['to_member', 'from_member']
    actions = [revert_transfer]


class JobDoneAdmin(BaseAdmin):
    list_display = ('id', 'created', 'job', 'handle', 'member', 'title', 'removed')
    list_editable = ()
    list_filter = ('created', 'job', 'title')
    search_fields = ('member__id', 'member__handle', 'member__username')
    readonly_fields = ['member',]


class GiftCodeAdmin(BaseAdmin):
    list_display = ('id', 'created', 'code', 'gift_coin', 'gift_character', 'redeem_count', 'count')
    list_editable = ()
    list_filter = ()


class RedeemAdmin(BaseAdmin):
    list_display = ('id', 'created', 'member', 'code')
    list_editable = ()
    search_fields = ('code__code', 'member__id', 'member__handle', 'member__username')
    list_filter = ()


class BazrasAdmin(BaseAdmin):
    list_display = ('id', 'created', 'bazras', 'bazras_handle', 'type', 'member', 'member_handle', 'time')
    list_editable = ()
    search_fields = ('bazras__id', 'bazras__handle', 'bazras__username', 'member__id', 'member__handle', 'member__username')
    list_filter = ('type',)



admin.site.register(BazrasReport, BazrasAdmin)
admin.site.register(OnlineStatistics, OnlineStatisticsAdmin)
admin.site.register(GameJoined, GameJoinedAdmin)
admin.site.register(GameContainer, GameContainerAdmin)
admin.site.register(Game, GameAdmin)
admin.site.register(GameSettings, GameSettingsAdmin)
admin.site.register(GameServer, GameServerAdmin)
admin.site.register(Map, MapAdmin)
admin.site.register(MapStage, MapStageAdmin)
admin.site.register(MessageReport, MessageReportAdmin)
admin.site.register(RequestError, RequestErrorAdmin)
admin.site.register(ContactUsMessage, ContactUsMessageAdmin)
admin.site.register(PurchaseWithCoin, PurchaseWithCoinAdmin)
admin.site.register(AddCoin, AddCoinAdmin)
admin.site.register(CoinTransfer, CoinTransferAdmin)
admin.site.register(JobDone, JobDoneAdmin)
admin.site.register(GiftCode, GiftCodeAdmin)
admin.site.register(Redeem, RedeemAdmin)
