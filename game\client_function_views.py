import json
import logging
from rest_framework import permissions
from rest_framework.decorators import api_view, permission_classes, renderer_classes
from rest_framework_jwt.settings import api_settings
from rest_framework_swagger import renderers
from django.http.response import JsonResponse
from django.db.models import Q, Count
from django.core.cache import cache

from account.utils import is_key_valid
from account.models.member import Member
from account.models.sync_data import SyncData
from account.serializers import MemberLeaderboardSerializer, MemberTransferSerializer, MemberCharacterLeaderboardSerializer
from shop.models import CharacterOwn
from shop.serializers import CharacterSerializer
from .serializers import GameServerSerializer, GameJoinedSerializer, DetailedGameServerSerializer, CoinTransferSerializer
from .models import <PERSON><PERSON>ontaine<PERSON>, GameServer, STATE_LOBBY, Game, GameJoined, MessageReport, RequestError, MODE_FREERIDE, MODE_RACE, ContactUsMessage, MODE_PROPHAUNT
from .models import <PERSON>ur<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Done, GiftCode, <PERSON><PERSON>m, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from police.models import P<PERSON><PERSON><PERSON>_TITLES,DOCTOR_TITLES, PoliceReport
from police.serailizers import PoliceReportSerializer
from utils.utils import send_email

logger = logging.getLogger('django')
LEADERBOARD_CACHE_TIME = 15 * 60


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def list_freeride_servers(request):
    member:Member = request.user
    data = request.data

    if not is_key_valid(member, data.get("key", "")) or member.full_ban:
        #send_email.delay("<EMAIL>", "Key Error", member.username + " " + str(member.id))
        logger.info("Secret Key Error: list " + str(member.id))
        return JsonResponse(status=400, data={"message": "GTFO"})
    

    if member.is_test:
        gss = GameServer.objects.filter(mode=MODE_FREERIDE, state=STATE_LOBBY, is_alive=True, hide=False).order_by("-players_joined")
    else:
        gss = GameServer.objects.filter(mode=MODE_FREERIDE, state=STATE_LOBBY, is_alive=True, is_test=False).order_by("-players_joined")
    
    member.find_game = True
    member.find_game_count += 1
    member.save()

    if len(gss) == 0:
        return JsonResponse(status=400, data={"message": "No Game Server available"})
    

    return JsonResponse(status=200, data={
        "servers": DetailedGameServerSerializer(gss, many=True).data
        })


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def find_game_server_v2(request):
    member = request.user
    if member.is_test:
        gss = GameServer.objects.filter(mode=MODE_RACE, state=STATE_LOBBY, is_alive=True, current_stage=1, is_test=member.is_test).order_by("id")
    else:
        gss = GameServer.objects.filter(mode=MODE_RACE, state=STATE_LOBBY, current_stage=1, is_alive=True, is_test=False).order_by("id")
    
    member.find_game = True
    member.find_game_count += 1
    member.save()

    best_server = None
    full_count = 1000
    for game in gss:
        if game.players_joined >= 20:
            continue
        if full_count > game.min_players_start - game.players_joined:
            full_count = game.min_players_start - game.players_joined
            best_server = game
    
    if best_server is None:
        return JsonResponse(status=400, data={"message": "No Game Server available"})
    

    return JsonResponse(status=200, data={
        "best_server": GameServerSerializer(best_server).data,
        "all_servers": GameServerSerializer(gss, many=True).data
        })


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def find_prophaunt_game_server(request):
    member = request.user
    if member.is_test:
        gss = GameServer.objects.filter(mode=MODE_PROPHAUNT, state=STATE_LOBBY, is_alive=True, current_stage=1, is_test=member.is_test).order_by("id")
    else:
        gss = GameServer.objects.filter(mode=MODE_PROPHAUNT, state=STATE_LOBBY, current_stage=1, is_alive=True, is_test=False).order_by("id")
    
    member.find_game = True
    member.find_game_count += 1
    member.save()

    best_server = None
    full_count = 1000
    for game in gss:
        if game.players_joined >= 20:
            continue
        if full_count > game.min_players_start - game.players_joined:
            full_count = game.min_players_start - game.players_joined
            best_server = game
    
    if best_server is None:
        return JsonResponse(status=400, data={"message": "No Game Server available"})
    

    return JsonResponse(status=200, data={
        "best_server": GameServerSerializer(best_server).data,
        "all_servers": GameServerSerializer(gss, many=True).data
        })


#Deprecated
@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def find_game_server(request):
    member = request.user
    if member.is_test:
        gss = GameServer.objects.filter(state=STATE_LOBBY, is_alive=True, is_test=member.is_test).order_by("id")
    else:
        gss = GameServer.objects.filter(state=STATE_LOBBY, is_alive=True, is_test=False).order_by("id")
    
    member.find_game = True
    member.find_game_count += 1
    member.save()

    best_server = None
    full_count = 1000
    for game in gss:
        if full_count > game.min_players_start - game.players_joined:
            full_count = game.min_players_start - game.players_joined
            best_server = game
    
    if best_server is None:
        return JsonResponse(status=400, data={"message": "No Game Server available"})
    
    return JsonResponse(status=200, data=GameServerSerializer(best_server).data)


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def game_results(request):
    data = request.data

    joineds = GameJoined.objects.filter(game_id=data["game_id"], finished=True).order_by("rank")

    
    return JsonResponse(status=200, data={"ranks": GameJoinedSerializer(joineds, many=True).data})



@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def container_results(request):
    data = request.data
    member = request.user

    #logger.info("Results v2: ")
    #logger.info(data)

    container = GameContainer.objects.get(id=data["container_id"])
    games = Game.objects.filter(game_container=container)
    game_ids = []
    for g in games:
        game_ids.append(g.id)

    joineds = GameJoined.objects.filter(game_id__in=game_ids, finished=True)
    
    my_result = {
        "coin": 0,
        "cup": 0,
        "crown": 0
    }
    winner = None
    for join in joineds:
        if join.crown == 1:
            winner = GameJoinedSerializer(join).data
        if join.is_bot:
            continue
        if join.member.id == member.id:
            my_result["coin"] += join.coin
            my_result["cup"] += join.cup
            my_result["crown"] += join.crown
        
    return JsonResponse(status=200, data={
        "my_result": my_result,
        "winner": winner
    })



@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def coin_leaderboard(request):
    members = Member.objects.filter(hide_in_ranks=False).order_by("-coin")[:100]

    if cache.get("coin_leaderboard") is None:
        res = {
                    "ranks": MemberLeaderboardSerializer(members, many=True).data,
                    "my_rank": 0,
              }
        cache.set("coin_leaderboard", res, LEADERBOARD_CACHE_TIME)
    else:
        res = cache.get("coin_leaderboard")
    return JsonResponse(status=200, data=res)


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def character_leaderboard(request):
    data = request.data
    member = request.user


    members = Member.objects.filter(hide_in_ranks=False).annotate(num_chars=Count('characters')).order_by('-num_chars')[:100]

    if cache.get("character_leaderboard") is None:
        result = []
        for m in members:
            result.append({
                "id": m.id,
                "handle": m.handle,
                "title": m.title,
                "num_chars": m.num_chars
            })
        res = {
                "ranks": result,
              }
        cache.set("character_leaderboard", res, LEADERBOARD_CACHE_TIME)
    else:
        res = cache.get("character_leaderboard")
    return JsonResponse(status=200, data=res)


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def leaderboard_v2(request):
    data = request.data

    if cache.get("leaderboard_v2") is None:
        members = Member.objects.order_by("-cup")[:100]
        win_members = Member.objects.order_by("-crown")[:100]
        res = {
                    "ranks": MemberLeaderboardSerializer(members, many=True).data,
                    "wins": MemberLeaderboardSerializer(win_members, many=True).data,
              }
        cache.set("leaderboard_v2", res, LEADERBOARD_CACHE_TIME)
    else:
        res = cache.get("leaderboard_v2")
    return JsonResponse(status=200, data=res)


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def smart_leaderboard(request):
    data = request.data

    if cache.get("smart_leaderboard") is None:
        members = Member.objects.filter(hide_in_ranks=False).order_by("-smart")[:100]
        res = {
                            "ranks": MemberLeaderboardSerializer(members, many=True).data,
              }
        cache.set("smart_leaderboard", res, LEADERBOARD_CACHE_TIME)
    else:
        res = cache.get("smart_leaderboard")
    return JsonResponse(status=200, data=res)



@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def report_message(request):
    data = request.data
    member = request.user

    report = MessageReport()
    report.member = member
    report.content = data.get("content", {})
    report.message_create_time = data.get("create_time", None)
    report.message_id = data.get("message_id", 0)
    report.sender_id = data.get("sender_id", 0)
    report.nakama_username = data.get("nakama_username", 0)
    report.save()

    return JsonResponse(status=200, data={})


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def ban_user(request):
    data = request.data
    member = request.user

    if member.chat_admin == False:
        return JsonResponse(status=400, data={"message": "You are not admin."})



    content = json.loads(data.get("content", {}))
    if content.get("id", None) != None:
        member = Member.objects.get(username=content["id"])
        time = data.get("time", 10 * 60)
        if time < 30 * 60:
            time = 30 * 60
        member.add_ban(time, False)

        return JsonResponse(status=200, data={})

    return JsonResponse(status=400, data={"message": "ID not found in message"})


@api_view(['POST', ])
@permission_classes([])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def request_error(request):
    data = request.data
    
    instance = RequestError()
    instance.content = data
    instance.save()
    
    return JsonResponse(status=200, data={"message": "Success"})


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def contact_us(request):
    data = request.data
    member = request.user

    msg = ContactUsMessage()
    msg.member = member
    msg.message = data.get("message", "")
    msg.save()

    return JsonResponse(status=200, data={})


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def purchase_with_coin(request):
    data = request.data
    member = request.user

    price = data.get("price", 1000)
    forgiving = data.get("forgiving", False)

    if price < 0:
        return JsonResponse(status=200, data={
            "coin": member.coin
        })

    purchase_log = PurchaseWithCoin()
    purchase_log.member = member
    purchase_log.price = price
    purchase_log.forgiving = forgiving
    purchase_log.type = data.get("type", "general")
    purchase_log.secondary = data.get("secondary", "")


    if forgiving:
        purchase_log.success = True
        purchase_log.save()

        if member.coin >= price:
            member.coin -= price
        else:
            member.coin = 0
        member.save()
        return JsonResponse(status=200, data={
            "coin": member.coin
        })

    #Not Forgiving
    if member.coin < price:
        purchase_log.success = False
        purchase_log.save()
        return JsonResponse(status=401, data={
            "message": "Not Enough Coin!"
        })

    member.coin -= price
    member.save()
    purchase_log.success = True
    purchase_log.save()

    return JsonResponse(status=200, data={
            "coin": member.coin
        })


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def add_coin(request):
    data = request.data
    member = request.user

    if member.full_ban:
        return JsonResponse(status=400, data={
                    "message": "GTFO"
                })


    value = data.get("value", 0)

    if value != 0:
        return JsonResponse(status=400, data={
                    "message": "GTFO"
                })
    if data.get("type", "") != "RewardVideo":
        return JsonResponse(status=400, data={
            "message": "GTFO"
        })


    if value >= 15000:
        return JsonResponse(status=400, data={
                    "message": "GTFO"
                })

    if member.full_ban:
        return JsonResponse(status=400, data={
                    "message": "GTFO"
                })

    if member.title in DOCTOR_TITLES:
        if data.get("type", "general") == "Salary":
            if value > 2000:
                return JsonResponse(status=200, data={
                    "coin": member.coin
                })


    coin_log = AddCoin()
    coin_log.member = member
    coin_log.value = value
    coin_log.type = data.get("type", "general")
    coin_log.secondary = data.get("secondary", "")
    coin_log.save()

    if coin_log.type == "RewardVideo" and value > 100:
        logger.info("RewardVideo coin error")
        return JsonResponse(status=400, data={
            "coin": member.coin
        })

    if coin_log.type == "Chest" and value > 500:
        logger.info("Chest coin error")
        return JsonResponse(status=400, data={
            "coin": member.coin
        })


    member.coin += value
    member.save()

    if value >= 5000:
        mail_body = str(value) + ": " + member.username + " " + member.handle + " " + str(member.coin) + " " + coin_log.type + " " + coin_log.secondary
        send_email.delay("<EMAIL>", "Huge AddCoin", mail_body)
    else:
        if data.get("type", "general") not in ["general", "Chest", "Salary", "CannonDart", "BattleHeroes", "RPS", "XO", "RewardVideo", "CreatorCashout", "DailyReward", "Job", "Progression", "Race", "StartMoney"]:
            mail_body = str(value) + ": " + member.username + " " + member.handle + " " + str(member.coin) + " " + coin_log.type + " " + coin_log.secondary
            send_email.delay("<EMAIL>", "Gift AddCoin!!!", mail_body)

    return JsonResponse(status=200, data={
            "coin": member.coin
        })


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def free_admin_ban_user(request):
    data = request.data
    member = request.user

    if member.free_admin == False and member.bazras == False:
        return JsonResponse(status=400, data={"message": "You are not admin."})
    
    if data.get("backend_id", None) != None:
        member = Member.objects.get(id=data["backend_id"])
    else:
        member = Member.objects.get(username=data["username"])
    
    member.add_ban(3600 * data.get("time", 1), False)

    report = BazrasReport()
    report.bazras = request.user
    report.type = "Ban"
    report.member = member
    report.time = 3600 * data.get("time", 1)
    report.save()

    return JsonResponse(status=200, data={})


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def free_admin_search(request):
    data = request.data
    member:Member = request.user

    if member.free_admin == False and member.elite_bazras == False:
        return JsonResponse(status=400, data={"message": "You are not admin."})
    

    members = Member.objects.filter(username=data.get("username", ""))
    if len(members) == 0:
        return JsonResponse(status=400, data={"message": "not found"})
    
    guilty:Member = members[0]

    return JsonResponse(status=200, data={
        "username": guilty.username,
        "handle": guilty.handle,
        "title": guilty.title,
        "ban_time": guilty.ban_time,
        "id": guilty.id,
        "police": guilty.title in POLICE_TITLES,
    })


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def free_admin_policereport_list(request):
    data = request.data
    member:Member = request.user

    if member.free_admin == False and member.elite_bazras == False:
        return JsonResponse(status=400, data={"message": "You are not admin."})
    

    members = Member.objects.filter(username=data.get("username", ""))
    if len(members) == 0:
        return JsonResponse(status=400, data={"message": "not found"})
    
    police:Member = members[0]

    reports = PoliceReport.objects.filter(reporter=police).order_by("-id")[:50]
    return JsonResponse(status=200, data={
        "reports": PoliceReportSerializer(reports, many=True).data,
    })


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def free_admin_unban_user(request):
    data = request.data
    member = request.user

    if member.free_admin == False and member.bazras == False:
        return JsonResponse(status=400, data={"message": "You are not admin."})


    if data.get("backend_id", None) != None:
        member = Member.objects.get(id=data["backend_id"])
    else:
        member = Member.objects.get(username=data["username"])
    

    member.ban_till_time = None
    member.save()

    report = BazrasReport()
    report.bazras = request.user
    report.type = "UnBan"
    report.member = member
    report.time = 0
    report.save()

    return JsonResponse(status=200, data={})


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def free_admin_set_title(request):
    data = request.data
    member = request.user

    if member.free_admin == False and member.bazras == False:
        return JsonResponse(status=400, data={"message": "You are not admin."})


    if data.get("backend_id", None) != None:
        member = Member.objects.get(id=data["backend_id"])
    else:
        member = Member.objects.get(username=data["username"])
    
    member.title = data.get("title", "")
    member.title_color = data.get("title_color", "FFFFFF")
    member.save()

    report = BazrasReport()
    report.bazras = request.user
    report.type = "SetTitle"
    report.member = member
    report.time = 0
    report.save()

    return JsonResponse(status=200, data={})


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def fetch_member_by_username(request):
    data = request.data
    member = request.user

    members = Member.objects.filter(username=data.get("username", ""))
    if len(members) == 0:
        return JsonResponse(status=400, data={"message": "Member Not Found"})

    to_member = members[0]
    if to_member == member:
        return JsonResponse(status=400, data={"message": "Member Not Found"})
    return JsonResponse(status=200, data=MemberTransferSerializer(to_member).data)


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def transfer_money(request):
    data = request.data
    member = request.user

    sync_data = SyncData.objects.get(id=1)

    if data.get("amount", 0) < 0:
        return JsonResponse(status=400, data={"message": "HaHa"})

    if member.cup < 100:
        return JsonResponse(status=400, data={"message": "Min Cup Error"})


    transfer_log = CoinTransfer()
    transfer_log.from_member = member
    transfer_log.from_coin_start = member.coin
    transfer_log.from_coin_end = member.coin
    transfer_log.amount = data.get("amount", 0)
    if transfer_log.amount < 100:
        transfer_log.fee = sync_data.coin_transfer_fee
    else:
        transfer_log.fee = int(transfer_log.amount / 10)

    to_members = Member.objects.filter(id=data.get("to_backend_id", 0))
    if len(to_members) == 0:
        transfer_log.save()
        return JsonResponse(status=400, data={"message": "Member Not Found"})

    to_member = to_members[0]
    if to_member == member:
        return JsonResponse(status=400, data={"message": "Member Not Found"})

    transfer_log.to_member = to_member
    transfer_log.to_coin_start = to_member.coin
    transfer_log.to_coin_end = to_member.coin
    
    if member.coin < transfer_log.fee + transfer_log.amount:
        transfer_log.save()
        return JsonResponse(status=401, data={"message": "Not Enough Coins"})

    member.coin -= transfer_log.fee + transfer_log.amount
    to_member.coin += transfer_log.amount
    member.save()
    to_member.save()
    transfer_log.from_coin_end = member.coin
    transfer_log.to_coin_end = to_member.coin
    transfer_log.success = True
    transfer_log.save()
    purchase_log = PurchaseWithCoin()
    purchase_log.member = member
    purchase_log.price = sync_data.coin_transfer_fee
    purchase_log.forgiving = False
    purchase_log.type = "MoneyTransfer"
    purchase_log.secondary = ""
    purchase_log.success = True
    purchase_log.save()
    return JsonResponse(status=200, data={"message": "Success"})


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def list_transactions(request):
    data = request.data
    member = request.user

    transactions = CoinTransfer.objects.filter(Q(from_member=member) | Q(to_member=member)).filter(success=True).order_by('-created')[:20]
    if len(transactions) == 0:
        return JsonResponse(status=410, data={"message": "No Transactions"})
    

    return JsonResponse(status=200, data={
        "transactions": CoinTransferSerializer(transactions, many=True).data
        })


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def job_done(request):
    member = request.user
    data = request.data
    job = JobDone()
    job.member = member
    job.title = member.title
    job.job = data.get("job", "")
    job.save()

    return JsonResponse(status=200, data={"message": "Success"})


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def hire_me(request):
    data = request.data
    member:Member = request.user

    if member.is_ban:
        return JsonResponse(status=411, data={"message": "GTFO"})

    to_title = data.get("job", "")["title"]
    police_titles = POLICE_TITLES
    if to_title in police_titles:
        if not member.police_job_enabled:
            return JsonResponse(status=410, data={"message": "License Needed"})


    price = data.get("price", 100)
    if member.coin < price:
         return JsonResponse(status=400, data={"message": "Not Enough Coin."})
    current_job = member.title
    title_data = data["job"]
    member.coin -= price
    member.title = title_data.get("title", "")
    member.title_color = title_data.get("color", "FFFFFF")
    member.save()
    purchase_log = PurchaseWithCoin()
    purchase_log.member = member
    purchase_log.price = price
    purchase_log.forgiving = False
    purchase_log.success = True
    if member.title == "":
        purchase_log.type = "QuitJob"
    else:
        purchase_log.type = "Hire"
    if data.get("promote", False):
        purchase_log.type = "Promote"
    purchase_log.secondary = current_job
    purchase_log.save()
    
    return JsonResponse(status=200, data={})


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def my_job_stats(request):
    data = request.data
    member = request.user

    price = data.get("price", 5)
    if member.coin < price:
         return JsonResponse(status=400, data={"message": "Not Enough Coin."})
    current_job = member.title
    member.coin -= price
    member.save()

    restaurant = 0
    hospital = 0
    bank = 0
    police = 0
    jobs = JobDone.objects.filter(member=member)
    for j in jobs:
        if j.job in ["cook", "clean", "menu", "deliver"]:
            restaurant += 1
        if j.job in ["visit", "heal"]:
            hospital += 1
        if j.job in ["cash"]:
            bank += 1
        if j.job in ["policeReport"]:
            police += 1
    

    purchase_log = PurchaseWithCoin()
    purchase_log.member = member
    purchase_log.price = price
    purchase_log.forgiving = False
    purchase_log.success = True
    purchase_log.type = "JobStat"
    purchase_log.secondary = str(restaurant) + " " + str(hospital) + " " + str(bank)
    purchase_log.save()
    
    return JsonResponse(status=200, data={
        "restaurant": restaurant,
        "hospital": hospital,
        "bank": bank,
        "police": police,
    })


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def redeem(request):
    member = request.user
    data = request.data

    code = data.get("code", "")
    gifts = GiftCode.objects.filter(code=code)
    if len(gifts) == 0:
        return JsonResponse(status=400, data={"message": "Gift Code Not Found."})

    gift_code = gifts[0]    
    redeem_count = gift_code.redeem_count
    if redeem_count >= gift_code.count:
        return JsonResponse(status=401, data={"message": "Gift Code Finished."})

    redeems = Redeem.objects.filter(member=member, code=gift_code)
    if len(redeems) > 0:
        return JsonResponse(status=402, data={"message": "Already Redeemed."})
    

    result = {}
    if gift_code.gift_character is None:
        #Coin gift
        member.coin += gift_code.gift_coin
        member.save()
        result["gift_coin"] = gift_code.gift_coin
    else:
        owns = CharacterOwn.objects.filter(member=member, character=gift_code.gift_character)
        if len(owns) > 0:
            result["gift_coin"] = 100
        else:
            own = CharacterOwn()
            own.member = member
            own.character = gift_code.gift_character
            own.save()
            result["gift_character"] = CharacterSerializer(gift_code.gift_character).data

    
    result["coin"] = member.coin

    gift_code.redeem_count += 1
    gift_code.save()
    redeem = Redeem()
    redeem.member = member
    redeem.code = gift_code
    redeem.save()


    return JsonResponse(status=200, data=result)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def get_freeride_server_owner(request):
    member = request.user
    data = request.data
    
    game_server = GameServer.objects.filter(id=data.get("id", 0)).first()
    if not game_server:
        return JsonResponse(status=401, data={"message": "Freeride server not found."})

    owner = game_server.owner

    is_admin = False
    is_admin = (member.free_admin)
    if not owner is None:
        is_admin = is_admin or (member.id == owner.id)

    response_data = {
        "owner_handle": game_server.owner_handle,
        "is_private": game_server.is_private,
        "remaining": game_server.remaining_time,
        "admin": is_admin,
    }

    return JsonResponse(status=200, data=response_data)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def change_server_password(request):
    member = request.user
    data = request.data
    
    server_id = data.get("server_id")
    new_password = data.get("new_password")
    
    if not server_id or not new_password:
        return JsonResponse(status=400, data={"message": "Server ID and new password are required."})
    
    game_server = GameServer.objects.filter(id=server_id).first()
    if not game_server:
        return JsonResponse(status=401, data={"message": "Game server not found."})
    
    if not member.free_admin:
        if game_server.owner != member:
            return JsonResponse(status=403, data={"message": "You are not the owner of this server."})
    
    
    game_server.password = new_password
    game_server.has_password = True
    game_server.save()
    
    return JsonResponse(status=200, data={"message": "Password changed successfully."})


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def change_server_capacity(request):
    member = request.user
    data = request.data
    
    server_id = data.get("server_id")
    capacity = data.get("capacity", None)
    
    if not server_id or not capacity:
        return JsonResponse(status=400, data={"message": "Server ID and capacity are required."})
    
    game_server = GameServer.objects.filter(id=server_id).first()
    if not game_server:
        return JsonResponse(status=401, data={"message": "Game server not found."})
    
    if not member.free_admin:
        if game_server.owner != member:
            return JsonResponse(status=403, data={"message": "You are not the owner of this server."})
    
    
    game_server.max_players_start = capacity
    game_server.save()
    
    return JsonResponse(status=200, data={"message": "Server updated."})
