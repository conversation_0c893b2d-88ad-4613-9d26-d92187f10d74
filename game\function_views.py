import environ
import random
import logging
from datetime import timedelta
from rest_framework import permissions
from rest_framework.decorators import api_view, permission_classes, renderer_classes
from rest_framework_jwt.settings import api_settings
from rest_framework_swagger import renderers
from django.http.response import JsonResponse
from django.utils import timezone
from django.db.models import Sum

from account.models.member import Member
from account.models.sync_data import SyncData
from shop.models import Character
from .serializers import GameServerSerializer, MapSerializer, GameSettingsSerializer
from .models import MapStage, GameContainer, GameServer, Map, GameJoined, Game, GameSettings, STATE_COUNTDOWN, STATE_LOADING, AddCoin, MODE_RACE, STATE_LOBBY, MAP_TYPE_RACE, MAP_TYPE_PROPHAUNT
from .utils import calculate_cup, calculate_coin
from utils.utils import code_generator, send_email
from police.models import POLICE_TITLES,DOCTOR_TITLES

logger = logging.getLogger('django')

@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def register_game_server(request):
    data = request.data

    member:Member = request.user
    if not member.server_verified:
        return JsonResponse(status=400, data={"message": "Wrong Password"})


    gs = GameServer.objects.filter(ip=data["ip"], port=data["port"])
    if len(gs) > 0:
        gs = gs[0]
    else:
        gs = GameServer()
        gs.ip = data["ip"]
        gs.port = data["port"]
    
    gs.state = STATE_LOADING
    gs.is_alive = True
    gs.last_update = timezone.now()
    settings = GameSettings.objects.all()[0]
    gs.current_stage = 1
    gs.total_stage = settings.normal_total_stage
    gs.mode = data.get("mode", "Race")
    if gs.mode == "FreeRide":
        gs.server_key = code_generator(size=15)
    gs.save()

    container = GameContainer()
    container.game_server = gs
    container.save()
    
    result = GameServerSerializer(gs).data
    return JsonResponse(status=200, data=result)


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def update_state(request):
    data = request.data

    member:Member = request.user
    if not member.server_verified:
        return JsonResponse(status=400, data={"message": "Wrong Password"})

    gs = GameServer.objects.filter(id=data["id"])
    if len(gs) == 0:
        #logger.info("game server not found : " + str(id))
        #logger.info(data)
        return JsonResponse(status=404, data={"message": "Game Server Not Found."})
    
    gs = gs[0]
    gs.state = data["state"]
    gs.players_joined = data["players_joined"]
    gs.bot_count = data.get("bot_count", 0)
    gs.is_alive = True
    gs.last_update = timezone.now()
    gs.mode = data.get("mode", "Race")
    if data["map_id"] > 0:
        map = Map.objects.get(id=data["map_id"])
        gs.map = map
        gs.min_players_start = map.min_players_count
        gs.max_players_start = map.max_players_count
    gs.save()

    if gs.players_joined == 0 and gs.mode == MODE_RACE and gs.current_stage > 1 and gs.state == STATE_LOBBY:
        return JsonResponse(status=401, data={"message": "Bad Race Server."})


    return JsonResponse(status=200, data={
        "force_new_player": gs.force_new_player,
        "force_reconnect": gs.force_reconnect,
        "max_players_count": gs.max_players_start,
        "gun_allow": gs.gun_allow,
        "max_vehicle": gs.max_vehicle,
        })


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def list_maps(request):
    return JsonResponse(status=200, data={"maps": MapSerializer(Map.objects.filter(is_active=True), many=True).data})


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def select_map(request):
    data = request.data
    gs = GameServer.objects.filter(id=data.get("id", 0))
    stage = 1
    if len(gs) > 0:
        gs = gs[0]
        stage = gs.current_stage
    else:
        gs = None
        stage = 1
    

    if gs.current_stage > gs.total_stage or (gs.players_joined == 0 and gs.current_stage > 1):
        return JsonResponse(status=400, data={
            "message": "Restart ASAP"
        })
    

    map_stages = MapStage.objects.get(stage=stage)

    # Determine map type based on game server mode
    if gs and gs.mode == "PropHaunt":
        map_type = MAP_TYPE_PROPHAUNT
    else:
        map_type = MAP_TYPE_RACE  # Default to race for other modes

    if gs is None or (not gs.is_test):
        maps = map_stages.maps.all().filter(is_active=True, beta=False, type=map_type)
    else:
        maps = map_stages.maps.all().filter(is_active=True, is_test=True, type=map_type)

    weight = []
    for map in maps:
        weight.append(map.weight)
    

    #logger.info("select map: " + str(stage))
    #if gs is not None:
    #    logger.info(gs.__str__())
    #logger.info(str(len(maps)))

    selected_map = random.choices(maps, weights=weight, k=1)[0]

    if gs is not None:
        gs.map = selected_map
        gs.min_players_start = selected_map.min_players_count
        gs.max_players_start = selected_map.max_players_count
        gs.save()
        pass

    settings = GameSettings.objects.all()[0]
    result = {
        "map": MapSerializer(selected_map).data,
        "settings": GameSettingsSerializer(settings).data
    }
    
    return JsonResponse(status=200, data=result)


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def select_prophunt_map(request):
    """Select a map specifically for PropHunt game mode"""
    data = request.data
    gs = GameServer.objects.filter(id=data.get("id", 0))

    if len(gs) > 0:
        gs = gs[0]
        # Ensure this is a PropHunt server
        if gs.mode != "PropHaunt":
            return JsonResponse(status=400, data={
                "message": "This endpoint is only for PropHunt servers"
            })
    else:
        return JsonResponse(status=404, data={
            "message": "Game server not found"
        })

    # Get PropHunt maps only
    if not gs.is_test:
        maps = Map.objects.filter(is_active=True, beta=False, type=MAP_TYPE_PROPHAUNT)
    else:
        maps = Map.objects.filter(is_active=True, is_test=True, type=MAP_TYPE_PROPHAUNT)

    if not maps.exists():
        return JsonResponse(status=404, data={
            "message": "No PropHunt maps available"
        })

    # Select map based on weight
    weight = [map.weight for map in maps]
    selected_map = random.choices(maps, weights=weight, k=1)[0]

    # Update game server with selected map
    gs.map = selected_map
    gs.min_players_start = selected_map.min_players_count
    gs.max_players_start = selected_map.max_players_count
    gs.save()

    settings = GameSettings.objects.all()[0]
    result = {
        "map": MapSerializer(selected_map).data,
        "settings": GameSettingsSerializer(settings).data,
        "map_type": "PropHaunt"
    }

    return JsonResponse(status=200, data=result)


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def start_game(request):
    data = request.data

    member:Member = request.user
    if not member.server_verified:
        return JsonResponse(status=400, data={"message": "Wrong Password"})

    sync_data = SyncData.objects.get(id=1)
    game_server = GameServer.objects.get(id=data["game_server_id"])

    game_server.state = STATE_COUNTDOWN
    game_server.save()

    container = game_server.containers.all().order_by('-created')[0]

    game = Game()
    game.game_server = game_server
    game.stage = game_server.current_stage
    game.total_stage = game_server.total_stage
    game.game_container = container
    game.save()

    #logger.info("Start Game! " +  game_server.__str__() + " (" + str(game_server.current_stage) + ")")
    #logger.info("ids count = " + str(len(data["ids"])))
    #logger.info(data)

    for packet in data["ids"]:
        if packet.get("character_id", None) == None or packet.get("id", None) == None:
            continue
        gj = GameJoined()
        if game_server.ip == "127.0.0.1":
            gj.character = None
        else:
            gj.character = Character.objects.get(id=packet["character_id"])
        if packet["id"] > 0:
            gj.member_id = packet["id"]
        else:
            gj.member = None
            gj.is_bot = True
            gj.bot_id = packet["id"]
        gj.game = game
        gj.handle = packet["handle"]
        gj.map = game_server.map
        gj.save()
        if gj.member != None:
            gj.member.coin -= sync_data.game_tax
            gj.member.game_count += 1
            gj.member.save()
    

    player_count = data.get("player_count", 2)
    qualified_count = 0
    if (game_server.current_stage != 1 and (game_server.current_stage == game_server.total_stage)) or player_count == 1:
        qualified_count = 1
    elif player_count <= 4:
        qualified_count = player_count - 1
    elif player_count > 4 and player_count <= 10:
        qualified_count = player_count - 3
    elif player_count > 10 and player_count <= 15:
        qualified_count = player_count - 6
    else:
        qualified_count = max(player_count - 10, 0)

    result = {
        "container_id": container.id,
        "game_id": game.id,
        "qualified_count": qualified_count
    }
    return JsonResponse(status=200, data=result)


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def finish_game(request):
    data = request.data

    member:Member = request.user
    if not member.server_verified:
        return JsonResponse(status=400, data={"message": "Wrong Password"})

    game = Game.objects.get(id=data["game_id"])
    game.is_finished = True
    game.finished = timezone.now()
    game.save()
    settings = GameSettings.objects.all()[0]

    container_finished = False

    players_count = len(GameJoined.objects.filter(game=game)) + game.game_server.bot_count
    players_finish_count = len(data["ids"])

    #logger.info("finish game request start: " + game.game_server.__str__() + " (" + str(game.game_server.current_stage) + ")")
    #logger.info(data)

    max_coin = 30
    if game.game_server.current_stage == 1 or game.game_server.current_stage == 2:
        max_coin = game.game_server.current_stage * 2

    total_qualified = 0
    for object in data["ids"]:
        if object["rank"] > 0:
            total_qualified += 1

    for object in data["ids"]:
        if object["id"] <= 0:
            try:
                gj = GameJoined.objects.get(game=game, member=None, bot_id=object["id"])
            except:
                #logger.info("Exception reached! : " + str(object["id"]))
                gj = None
                continue
        else:
            try:
                gj = GameJoined.objects.get(game=game, member__id=object["id"])
            except:
                #logger.info("#2 Exception reached Should create! : " + str(object["id"]))
                gj = GameJoined()
                gj.game = game
                gj.member_id = object["id"]
                gj.map = game.game_server.map

        if object["rank"] != -1:
            gj.finished = True
            gj.rank = object["rank"]
            gj.time = object["time"]
            gj.cup = calculate_cup(players_count=players_count, players_finish_count=players_finish_count, rank=gj.rank)
            gj.coin = calculate_coin(players_count=players_count, players_finish_count=players_finish_count, rank=gj.rank, coin_multi=settings.coin_multiplier, max_coin=max_coin)
            if gj.member:
                coin_log = AddCoin()
                coin_log.member = gj.member
                coin_log.value = gj.coin
                coin_log.type = "Race"
                coin_log.secondary = str(gj.rank)
                coin_log.save()
            if game.game_server.current_stage == game.game_server.total_stage or total_qualified == 1:
                gj.crown = 1
                container_finished = True
        gj.fps = object.get("fps", -1)
        gj.fps_count = object.get("fps_count", -1)
        gj.ping = object.get("ping", -1)
        gj.ping_count = object.get("ping_count", -1)
        gj.save()
        if object["id"] > 0 and object["rank"] != -1:
            member = gj.member
            member.add_cup_and_coin(gj.cup, gj.coin)
            member.win_count += 1
            if object["rank"] == 1:
                member.first_place_count += 1
                if game.game_server.current_stage == game.game_server.total_stage or total_qualified == 1:
                    member.crown += 1
                    container_finished = True
            member.save()


    game_server = game.game_server
    game_server.current_stage += 1
    if game_server.current_stage > game.game_server.total_stage or container_finished or len(data["ids"]) == 0:
        game_server.current_stage = 1
    game_server.save()

    return JsonResponse(status=200, data={"message": "success"})


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def freeride_add_coin(request):
    data = request.data

    server_key = data.get("server_key", "")

    gss = GameServer.objects.filter(server_key=server_key)
    if len(gss) == 0:
        return JsonResponse(status=400, data={
                    "message": "GTFO1"
                })
    gs:GameServer = gss[0]
    if not gs.trusted:
        return JsonResponse(status=400, data={
                    "message": "GTFO2"
                })
    members = Member.objects.filter(id=data.get("player_backend_id", 0))
    if len(members) == 0:
        return JsonResponse(status=400, data={
                    "message": "Member not found"
                })
    member:Member = members[0]
    value = data.get("value", 0)
    type = data.get("type", "general")

    if member.full_ban:
        return JsonResponse(status=200, data={
                    "message": "GTFO"
                })

    #if member.title in DOCTOR_TITLES:
    #    if data.get("type", "general") == "Salary":
    #        if value > 2000:
    #            return JsonResponse(status=200, data={
    #                "coin": member.coin
    #            })
    
    if value >= 15000:
        mail_body = str(value) + ": " + member.username + " " + member.handle + " " + str(member.coin) + " " + type + " " + data.get("secondary", "")
        send_email.delay("<EMAIL>", "STOP AddCoin", mail_body)
        return JsonResponse(status=200, data={
                    "coin": member.coin
                })


    if  type == "Salary":
        count = AddCoin.objects.filter(member=member, created__gt=timezone.now() - timedelta(hours=1)).count()
        if count > 40:
            mail_body = str(value) + ": " + member.username + " " + member.handle + " " + str(member.coin) + " " + data.get("type", "general") + " " + member.title
            #send_email.delay("<EMAIL>", "TOO Much Salary in 1H", mail_body)
            return JsonResponse(status=200, data={
                    "coin": member.coin,
                    "message": "Salary limit reached."
                })
    

    sum = AddCoin.objects.filter(member=member, created__gt=timezone.now() - timedelta(hours=24)).aggregate(sum=Sum('value'))["sum"]
    if sum == None:
        sum = 0
    if sum > 20000:
        delta = (timezone.now() - member.last_suspicous_email)
        if delta.total_seconds() >= 24 * 3600:
            add_coins = AddCoin.objects.filter(member=member, created__gt=timezone.now() - timedelta(hours=8))
            mail_body = str(member.id) + " total=" + str(sum) + "\n"
            for add in add_coins:
                mail_body = mail_body + str(value) + ": " + member.username + " " + member.handle + " " + str(add.value) + " " + add.type + " title=" + member.title + " coins=" + str(member.coin) + "\n"
            
            send_email.delay("<EMAIL>", "TOO Much Addcoin in 8h", mail_body)
            member.last_suspicous_email = timezone.now()
            member.save()



    coin_log = AddCoin()
    coin_log.member = member
    coin_log.value = value
    coin_log.type = data.get("type", "general")
    coin_log.secondary = data.get("secondary", "")
    if coin_log.secondary == "":
        coin_log.secondary = member.title
    coin_log.save()

    if coin_log.type == "RewardVideo" and value > 100:
        logger.info("RewardVideo coin error")
        return JsonResponse(status=400, data={
            "coin": member.coin
        })
    

    if coin_log.type == "Chest" and value > 500:
        logger.info("Chest coin error")
        return JsonResponse(status=400, data={
            "coin": member.coin
        })


    member.coin += value
    member.save()

    if value >= 5000:
        mail_body = str(value) + ": " + member.username + " " + member.handle + " " + str(member.coin) + " " + coin_log.type + " " + coin_log.secondary
        send_email.delay("<EMAIL>", "Huge AddCoin", mail_body)
    else:
        if data.get("type", "general") not in ["general", "Chest", "Salary", "CannonDart", "BattleHeroes", "RPS", "XO", "RewardVideo", "CreatorCashout", "DailyReward", "Job", "Progression", "Race", "StartMoney"]:
            mail_body = str(value) + ": " + member.username + " " + member.handle + " " + str(member.coin) + " " + coin_log.type + " " + coin_log.secondary
            send_email.delay("<EMAIL>", "Gift AddCoin!!!", mail_body)

    return JsonResponse(status=200, data={
            "coin": member.coin,
            "player_key": data.get("player_key", "")
        })
