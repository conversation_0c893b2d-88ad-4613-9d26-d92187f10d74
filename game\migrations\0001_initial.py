# Generated by Django 2.2.28 on 2023-07-15 21:03

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='GameServer',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('removed', models.DateTimeField(blank=True, default=None, editable=False, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('last_update', models.DateTimeField(auto_now=True)),
                ('ip', models.CharField(default='', max_length=100)),
                ('port', models.IntegerField(default=9000)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
