# Generated by Django 2.2.28 on 2023-07-17 13:06

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('game', '0005_gameserver_is_alive'),
    ]

    operations = [
        migrations.AlterField(
            model_name='gameserver',
            name='state',
            field=models.CharField(choices=[('Lobby', 'Lobby'), ('CountDown', 'CountDown'), ('InGame', 'InGame'), ('Results', 'Results'), ('Loading', 'Loading')], default='Lobby', max_length=30),
        ),
    ]
