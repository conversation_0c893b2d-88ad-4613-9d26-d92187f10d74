# Generated by Django 2.2.28 on 2023-09-30 14:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('game', '0019_gamejoined_coin'),
    ]

    operations = [
        migrations.CreateModel(
            name='GameSettings',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('removed', models.DateTimeField(blank=True, default=None, editable=False, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('last_update', models.DateTimeField(auto_now=True)),
                ('lobby_time', models.IntegerField(default=0)),
                ('game_time', models.IntegerField(default=0)),
                ('player_join_add_time', models.IntegerField(default=0)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
