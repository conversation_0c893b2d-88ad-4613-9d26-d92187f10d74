# Generated by Django 2.2.28 on 2023-11-01 16:04

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('game', '0034_map_beta'),
    ]

    operations = [
        migrations.CreateModel(
            name='MessageReport',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('removed', models.DateTimeField(blank=True, default=None, editable=False, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('last_update', models.DateTimeField(auto_now=True)),
                ('content', models.TextField(default='')),
                ('message_create_time', models.DateTimeField(default=django.utils.timezone.now)),
                ('message_id', models.CharField(default='', max_length=200)),
                ('sender_id', models.Char<PERSON>ield(default='', max_length=200)),
                ('nakama_username', models.Char<PERSON><PERSON>(default='', max_length=200)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
