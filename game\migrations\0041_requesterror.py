# Generated by Django 2.2.28 on 2024-01-08 14:15

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('game', '0040_messagereport_handled'),
    ]

    operations = [
        migrations.CreateModel(
            name='RequestError',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('removed', models.DateTimeField(blank=True, default=None, editable=False, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('last_update', models.DateTimeField(auto_now=True)),
                ('content', models.TextField(default='')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
