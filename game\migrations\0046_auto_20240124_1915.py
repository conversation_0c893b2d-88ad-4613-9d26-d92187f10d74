# Generated by Django 2.2.28 on 2024-01-24 15:45

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('game', '0045_gamejoined_crown'),
    ]

    operations = [
        migrations.CreateModel(
            name='GameContainer',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('removed', models.DateTimeField(blank=True, default=None, editable=False, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('last_update', models.DateTimeField(auto_now=True)),
                ('game_server', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='containers', to='game.GameServer')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='game',
            name='game_container',
            field=models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='games', to='game.GameContainer'),
        ),
    ]
