# Generated by Django 2.2.28 on 2024-01-25 14:05

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('game', '0046_auto_20240124_1915'),
    ]

    operations = [
        migrations.CreateModel(
            name='MapStage',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('removed', models.DateTimeField(blank=True, default=None, editable=False, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('last_update', models.DateTimeField(auto_now=True)),
                ('stage', models.IntegerField(default=1)),
                ('maps', models.ManyToManyField(blank=True, to='game.Map')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
