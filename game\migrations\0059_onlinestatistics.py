# Generated by Django 2.2.28 on 2024-08-08 15:03

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('game', '0058_auto_20240727_1518'),
    ]

    operations = [
        migrations.CreateModel(
            name='OnlineStatistics',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('removed', models.DateTimeField(blank=True, default=None, editable=False, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('last_update', models.DateTimeField(auto_now=True)),
                ('number_of_freeride_users', models.IntegerField(default=0)),
                ('number_of_race_users', models.IntegerField(default=0)),
                ('max_freeride_server_user', models.IntegerField(default=0)),
                ('max_race_server_user', models.IntegerField(default=0)),
                ('number_of_freeride_servers', models.IntegerField(default=0)),
                ('number_of_race_servers', models.IntegerField(default=0)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
