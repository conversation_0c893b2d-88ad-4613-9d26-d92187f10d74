# Generated by Django 2.2.28 on 2024-09-16 14:38

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('game', '0062_addcoin_secondary'),
    ]

    operations = [
        migrations.CreateModel(
            name='CoinTransfer',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('removed', models.DateTimeField(blank=True, default=None, editable=False, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('last_update', models.DateTimeField(auto_now=True)),
                ('amount', models.IntegerField(default=0)),
                ('fee', models.IntegerField(default=0)),
                ('from_coin_start', models.IntegerField(default=0)),
                ('from_coin_end', models.IntegerField(default=0)),
                ('to_coin_start', models.IntegerField(default=0)),
                ('to_coin_end', models.IntegerField(default=0)),
                ('success', models.BooleanField(default=False)),
                ('from_member', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='transfer_froms', to=settings.AUTH_USER_MODEL)),
                ('to_member', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='transfer_tos', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
