# Generated by Django 2.2.28 on 2025-01-11 16:14

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('game', '0072_gameserver_is_private'),
    ]

    operations = [
        migrations.AddField(
            model_name='gameserver',
            name='due_date',
            field=models.DateTimeField(blank=True, default=django.utils.timezone.now, null=True),
        ),
        migrations.AddField(
            model_name='gameserver',
            name='owner',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='owned_servers', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='gameserver',
            name='owner_note',
            field=models.TextField(blank=True, null=True),
        ),
    ]
