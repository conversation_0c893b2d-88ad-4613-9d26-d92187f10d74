# Generated by Django 2.2.28 on 2025-01-14 17:32

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('game', '0074_auto_20250111_1957'),
    ]

    operations = [
        migrations.AlterField(
            model_name='gameserver',
            name='map',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='game.Map'),
        ),
        migrations.CreateModel(
            name='BazrasReport',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('removed', models.DateTimeField(blank=True, default=None, editable=False, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('last_update', models.DateTimeField(auto_now=True)),
                ('type', models.CharField(blank=True, max_length=100, null=True)),
                ('message', models.TextField(blank=True)),
                ('time', models.IntegerField(default=0)),
                ('bazras', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bazras_reports', to=settings.AUTH_USER_MODEL)),
                ('member', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bazras_member_reports', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
