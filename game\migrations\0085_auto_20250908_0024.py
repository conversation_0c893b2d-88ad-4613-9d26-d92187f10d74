# Generated by Django 2.2.28 on 2025-09-07 20:54

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('game', '0084_auto_20250818_0039'),
    ]

    operations = [
        migrations.AddField(
            model_name='gamesettings',
            name='prophaunt_grenade_damage',
            field=models.FloatField(default=75.0),
        ),
        migrations.AddField(
            model_name='gamesettings',
            name='prophaunt_grenade_radius',
            field=models.FloatField(default=5.0),
        ),
        migrations.AddField(
            model_name='gamesettings',
            name='prophaunt_gun_damage',
            field=models.FloatField(default=25.0),
        ),
        migrations.AddField(
            model_name='gamesettings',
            name='prophaunt_hex_cooldown',
            field=models.FloatField(default=30.0),
        ),
        migrations.AddField(
            model_name='gamesettings',
            name='prophaunt_hex_duration',
            field=models.FloatField(default=4.0),
        ),
        migrations.AddField(
            model_name='gamesettings',
            name='prophaunt_rounds_per_game',
            field=models.IntegerField(default=3),
        ),
        migrations.AddField(
            model_name='gamesettings',
            name='prophaunt_self_damage',
            field=models.FloatField(default=10.0),
        ),
        migrations.AddField(
            model_name='gamesettings',
            name='prophaunt_show_period',
            field=models.FloatField(default=8.0),
        ),
        migrations.AddField(
            model_name='gamesettings',
            name='prophaunt_sound_cooldown',
            field=models.FloatField(default=4.0),
        ),
        migrations.AddField(
            model_name='gamesettings',
            name='prophaunt_stealth_period',
            field=models.FloatField(default=20.0),
        ),
    ]
