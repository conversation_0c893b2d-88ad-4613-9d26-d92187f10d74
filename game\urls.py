from django.urls import path
from .function_views import register_game_server, update_state, list_maps, select_map, start_game, finish_game, freeride_add_coin, select_prophunt_map
from .client_function_views import find_game_server, game_results, report_message, find_game_server_v2, ban_user, request_error, container_results, leaderboard_v2
from .client_function_views import list_freeride_servers, contact_us, purchase_with_coin, add_coin, free_admin_ban_user, free_admin_unban_user, free_admin_set_title
from .client_function_views import transfer_money, fetch_member_by_username, list_transactions, job_done, hire_me, my_job_stats, coin_leaderboard
from .client_function_views import redeem, character_leaderboard, get_freeride_server_owner, change_server_password, change_server_capacity, smart_leaderboard
from .client_function_views import free_admin_search, free_admin_policereport_list, find_prophaunt_game_server

urlpatterns = [
    path(r'register/', register_game_server, name="register"),
    path(r'update/', update_state, name="update_state"),
    path(r'list_maps/', list_maps, name="list_maps"),
    path(r'select_map/', select_map, name="select_map"),
    path(r'select_prophaunt_map/', select_prophunt_map, name="select_prophunt_map"),
    path(r'start_game/', start_game, name="start_game"),
    path(r'finish_game/', finish_game, name="finish_game"),
    path(r'freeride_add_coin/', freeride_add_coin, name="freeride_add_coin"),

    #Client
    path(r'list_freeride_servers/', list_freeride_servers, name="list_freeride_servers"),
    path(r'find_game_v2/', find_game_server_v2, name="find_game_server_v2"),
    path(r'find_game/', find_game_server, name="find_game_server"),
    path(r'find_prophaunt_game/', find_prophaunt_game_server, name="find_prophaunt_game"),
    path(r'results/', game_results, name="game_results"),
    path(r'results_v2/', container_results, name="container_results"),
    path(r'coin_leaderboard/', coin_leaderboard, name="coin_leaderboard"),
    path(r'char_leaderboard/', character_leaderboard, name="char_leaderboard"),
    path(r'leaderboard_v2/', leaderboard_v2, name="leaderboard_v2"),
    path(r'smart_leaderboard/', smart_leaderboard, name="smart_leaderboard"),
    path(r'report_message/', report_message, name="report_message"),
    path(r'ban_user/', ban_user, name="ban_user"),
    path(r'request_error/', request_error, name="request_error"),
    path(r'contact_us/', contact_us, name="contact_us"),
    path(r'purchase_with_coin/', purchase_with_coin, name="purchase_with_coin"),
    path(r'add_coin/', add_coin, name="add_coin"),
    path(r'free_admin_report_list/', free_admin_policereport_list, name="free_admin_report_list"),
    path(r'free_admin_ban/', free_admin_ban_user, name="free_admin_ban_user"),
    path(r'free_admin_unban/', free_admin_unban_user, name="free_admin_unban_user"),
    path(r'free_admin_set_title/', free_admin_set_title, name="free_admin_set_title"),
    path(r'free_admin_search/', free_admin_search, name="free_admin_search"),
    path(r'fetch_member_by_username/', fetch_member_by_username, name="fetch_member_by_username"),
    path(r'transfer_money/', transfer_money, name="transfer_money"),
    path(r'list_transactions/', list_transactions, name="list_transactions"),
    path(r'job_done/', job_done, name="job_done"),
    path(r'hire_me/', hire_me, name="hire_me"),
    path(r'my_job_stats/', my_job_stats, name="my_job_stats"),
    path(r'redeem/', redeem, name="redeem"),
    path(r'change_server_password/', change_server_password, name="change_server_password"),
    path(r'change_server_capacity/', change_server_capacity, name="change_server_capacity"),
    path(r'get_freeride_server_owner/', get_freeride_server_owner, name="get_freeride_server_owner"),
]
