DEFAULT_PARTICIPANT_COUNT = 10

def calculate_cup(players_count, players_finish_count, rank):
    if players_count >= DEFAULT_PARTICIPANT_COUNT:
        return max((DEFAULT_PARTICIPANT_COUNT - rank + 1) * 1, 1)

    return max((players_count - rank + 1) * 1, 1)


def calculate_coin(players_count, players_finish_count, rank, coin_multi=1, max_coin=1):
    return_value = 0
    if players_count >= DEFAULT_PARTICIPANT_COUNT:
        return_value = max((DEFAULT_PARTICIPANT_COUNT - rank + 1) * coin_multi, 1)
    else:
        return_value = max((players_count - rank + 1) * coin_multi, 1)
    
    return_value = min(return_value, max_coin)
    return return_value

