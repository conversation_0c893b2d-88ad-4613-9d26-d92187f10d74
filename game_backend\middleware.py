import logging

logger = logging.getLogger("django.request")

class StatusCodeLoggingMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)

        if response.status_code != 200:
            logger.warning(
                f"{request.method} {request.path} returned {response.status_code}",
                extra={
                    "status_code": response.status_code,
                    "method": request.method,
                    "path": request.path,
                    "user": getattr(request.user, "username", "Anonymous"),
                }
            )
        return response
