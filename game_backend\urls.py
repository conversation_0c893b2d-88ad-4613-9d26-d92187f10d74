"""game_backend URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/2.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    path('account/', include('account.urls')),
    path('game/', include('game.urls')),
    path('shop/', include('shop.urls')),
    path('police/', include('police.urls')),
    path('mobile/', include('mobile.urls')),
    path('voice/', include('audio_stream.urls')),
    path('minigame/', include('minigame.urls')),
    path('vehicle/', include('vehicle.urls')),
    path('prophunt/', include('prophunt.urls')),
]
