import json
from django.contrib import admin
from utils.admin import BaseAdmin
from account.models.member import Member
from .models import MiniGame, MiniGameJoin
from utils.utils import send_email



class MiniGameAdmin(BaseAdmin):
    list_display = ('id', 'created', 'price', 'type', 'state', 'godot_id', 'winner', 'game_server')
    list_editable = ()
    list_filter = ('type', 'state', 'created')
    raw_id_fields = ('winner',)
    actions = []


class MiniGameJoinAdmin(BaseAdmin):
    list_display = ('id', 'created', 'member', 'minigame', 'coin', 'smart', 'cup', 'time', 'wait_till_end')
    list_editable = ()
    list_filter = ('wait_till_end', 'created')
    raw_id_fields = ('member',)
    actions = []


admin.site.register(MiniGame, MiniGameAdmin)
admin.site.register(MiniGameJoin, MiniGameJoinAdmin)
