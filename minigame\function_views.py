import json
import logging
from datetime import timed<PERSON><PERSON>
from rest_framework import permissions
from rest_framework.decorators import api_view, permission_classes, renderer_classes
from rest_framework_jwt.settings import api_settings
from rest_framework_swagger import renderers
from django.http.response import JsonResponse
from django.utils import timezone
from django.db.models import Sum


from account.models import Member
from game.models import GameServer, AddCoin, PurchaseWithCoin
from utils.utils import send_email
from .models import MiniGame, MiniGameJoin, STATE_INGAME, STATE_LOBBY, STATE_RESULTS, GAME_TYPE_MENCH

logger = logging.getLogger('django')

@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def start_minigame(request):
    data = request.data

    member:Member = request.user
    if not member.server_verified:
        return JsonResponse(status=400, data={"message": "Wrong Password"})

    server_key = data.get("server_key", "")
    gss = GameServer.objects.filter(server_key=server_key)
    if len(gss) == 0:
        return JsonResponse(status=400, data={
                    "message": "GTFO1"
                })
    gs:GameServer = gss[0]
    if not gs.trusted:
        return JsonResponse(status=400, data={
                    "message": "GTFO2"
                })
    

    minigame = MiniGame()
    minigame.game_server = gs
    minigame.state = STATE_INGAME
    minigame.godot_id = data.get("godot_id", "")
    minigame.type = data.get("type", GAME_TYPE_MENCH)
    minigame.price = data.get("price", 50)
    minigame.save()

    all_success = True
    for player in data.get("players", []):
        members = Member.objects.filter(id=player)
        if len(members) == 0:
            all_success = False
            continue
        member = members[0]
        minigame_join = MiniGameJoin()
        minigame_join.member = member
        minigame_join.minigame = minigame
        minigame_join.save()

        purchase_log = PurchaseWithCoin()
        purchase_log.member = member
        purchase_log.price = min(member.coin, minigame.price)
        purchase_log.forgiving = False
        purchase_log.type = "Minigame"
        purchase_log.secondary = minigame.type
        purchase_log.after_coin = member.coin
        purchase_log.success = True
        purchase_log.save()

        member.coin -= minigame.price
        if member.coin < 0:
            member.coin = 0
        member.save()
    

    # if all_success == False:
    #     send_email.delay("<EMAIL>", "Minigame start Error ", "members: {}".format(data.get("players", [])))
    

    return JsonResponse(status=200, data={
                    "message": "success",
                    "minigame_id": minigame.id,
                    "godot_id": minigame.godot_id,
                })


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def end_minigame(request):
    data = request.data

    member:Member = request.user
    if not member.server_verified:
        return JsonResponse(status=400, data={"message": "Wrong Password"})

    server_key = data.get("server_key", "")
    gss = GameServer.objects.filter(server_key=server_key)
    if len(gss) == 0:
        return JsonResponse(status=400, data={
                    "message": "GTFO1"
                })
    gs:GameServer = gss[0]
    if not gs.trusted:
        return JsonResponse(status=400, data={
                    "message": "GTFO2"
                })
    

    minigame = MiniGame.objects.get(id=data.get("minigame_id", 0))


    all_success = True
    for player in data.get("players", []):
        members = Member.objects.filter(id=player["backend_id"])
        if len(members) == 0:
            all_success = False
            continue
        member = members[0]
        minigame_join = MiniGameJoin.objects.get(minigame=minigame, member=member)
        minigame_join.wait_till_end = True
        minigame_join.coin = player["coin"]
        minigame_join.cup = player["cup"]
        minigame_join.smart = player["smart"]
        minigame_join.time = player.get("time", 0.0)
        minigame_join.save()

        member.coin += player["coin"]
        member.cup += player["cup"]
        member.smart += player["smart"]
        member.save()


        if player["coin"] > 0:
            coin_log = AddCoin()
            coin_log.member = member
            coin_log.value = player["coin"]
            coin_log.type = "MiniGame"
            coin_log.secondary = minigame.type
            coin_log.save()

        
        if player["winner"]:
            minigame.winner = member
            minigame.save()
        
    
    if len(data.get("players", [])) > 0:
        minigame.success = True
        minigame.save()
    

    # if all_success == False:
    #     send_email.delay("<EMAIL>", "Minigame end Error ", "members: {}".format(data.get("players", [])))
    

    return JsonResponse(status=200, data={
                    "message": "success"
                })
