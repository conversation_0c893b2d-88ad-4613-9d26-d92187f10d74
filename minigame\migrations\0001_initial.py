# Generated by Django 2.2.28 on 2025-04-26 19:47

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('game', '0079_auto_20250403_1554'),
    ]

    operations = [
        migrations.CreateModel(
            name='MiniGameJoin',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('removed', models.DateTimeField(blank=True, default=None, editable=False, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('last_update', models.DateTimeField(auto_now=True)),
                ('coin', models.IntegerField(default=0)),
                ('cup', models.IntegerField(default=0)),
                ('smart', models.IntegerField(default=0)),
                ('member', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='minigamejoins', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='MiniGame',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('removed', models.DateTimeField(blank=True, default=None, editable=False, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('last_update', models.DateTimeField(auto_now=True)),
                ('state', models.CharField(choices=[('Lobby', 'Lobby'), ('InGame', 'InGame'), ('Results', 'Results')], default='Lobby', max_length=30)),
                ('success', models.BooleanField(default=False)),
                ('game_server', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='minigames', to='game.GameServer')),
                ('winner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='minigames', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
