import json
from django.db import models
from django.utils import timezone
from utils.models import BaseModel


GAME_TYPE_MENCH = "Mench"
GAME_TYPE_VEHICLE_RACE = "VehicleRace"
GAME_TYPE_CHOICES = (
    (GAME_TYPE_MENCH, GAME_TYPE_MENCH),
    (GAME_TYPE_VEHICLE_RACE, GAME_TYPE_VEHICLE_RACE),
)


STATE_LOBBY = "Lobby"
STATE_INGAME = "InGame"
STATE_RESULTS = "Results"


STATE_CHOICES = (
    (STATE_LOBBY, STATE_LOBBY),
    (STATE_INGAME, STATE_INGAME),
    (STATE_RESULTS, STATE_RESULTS),
)



class MiniGame(BaseModel):
    game_server = models.ForeignKey("game.GameServer", related_name='minigames', on_delete=models.CASCADE)
    type = models.CharField(max_length=30, choices=GAME_TYPE_CHOICES, default=GAME_TYPE_MENCH)
    price = models.IntegerField(default=0)
    state = models.CharField(max_length=30, choices=STATE_CHOICES, default=STATE_LOBBY)
    godot_id = models.CharField(max_length=30, default="")
    winner = models.ForeignKey("account.Member", related_name='minigames', on_delete=models.CASCADE, null=True, default=None)
    success = models.BooleanField(default=False)


    def __str__(self):
        return "(" + str(self.id) + ") " + self.type


class MiniGameJoin(BaseModel):
    member = models.ForeignKey("account.Member", related_name='minigamejoins', on_delete=models.CASCADE)
    minigame = models.ForeignKey(MiniGame, related_name='minigamejoins', on_delete=models.CASCADE, default=None)
    coin = models.IntegerField(default=0)
    cup = models.IntegerField(default=0)
    smart = models.IntegerField(default=0)
    time = models.FloatField(default=0)
    wait_till_end = models.BooleanField(default=False)
