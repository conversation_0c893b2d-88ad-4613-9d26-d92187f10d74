import json
from django.contrib import admin
from utils.admin import BaseAdmin

from .models import MobilePhone, Friendship, Clan, ClanMembership, ClanLevel, ClanTransaction


class ClanAdmin(BaseAdmin):
    list_display = ('id', 'name', 'level', 'member_count', 'type', 'bank', 'removed')
    list_editable = ()
    list_filter = ('type', 'level', 'removed')
    search_fields = ('name', )


class ClanMembershipAdmin(BaseAdmin):
    list_display = ('id', 'member', 'username', 'handle', 'clan', 'state', 'rank', 'removed')
    list_editable = ()
    list_filter = ('state', 'rank', 'removed')
    raw_id_fields = ('member',)
    search_fields = ('member__username', 'member__id', 'member__handle', 'clan__name')


class ClanLevelAdmin(BaseAdmin):
    list_display = ('id', 'level', 'max_count', 'price')
    list_editable = ('level', 'max_count', 'price')
    list_filter = ()


class FriendShipAdmin(BaseAdmin):
    list_display = ('id', 'member1_handle', 'member1_username', 'member2_handle', 'member2_username', 'state', 'removed')
    list_editable = ()
    list_filter = ('state', 'removed')
    raw_id_fields = ('member1', 'member2')
    search_fields = ('member1__username', 'member1__id', 'member1__handle', 'member2__username', 'member2__id', 'member2__handle')


class MobilePhoneAdmin(BaseAdmin):
    list_display = ('id', 'name', 'price', 'is_test')
    list_editable = ('is_test',)
    list_filter = ()


class ClanTransactionAdmin(BaseAdmin):
    list_display = ('id', 'member', 'handle', 'clan', 'amount', 'type', 'bank', 'member_coin')
    list_editable = ()
    list_filter = ('type', 'created')
    raw_id_fields = ('member',)
    search_fields = ('clan__id', 'clan__name')


admin.site.register(MobilePhone, MobilePhoneAdmin)
admin.site.register(Friendship, FriendShipAdmin)
admin.site.register(Clan, ClanAdmin)
admin.site.register(ClanTransaction, ClanTransactionAdmin)
admin.site.register(ClanMembership, ClanMembershipAdmin)
admin.site.register(ClanLevel, ClanLevelAdmin)
