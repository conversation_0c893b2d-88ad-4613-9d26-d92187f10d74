import logging
import random
from django.db.models import Sum, F, When

from rest_framework import permissions
from rest_framework.decorators import api_view, permission_classes, renderer_classes
from rest_framework_jwt.settings import api_settings
from rest_framework_swagger import renderers
from django.http.response import JsonResponse
from django.db.models import Q, Count
from django.core.cache import cache

from account.models.member import Member
from .models import STATE_INVITE, STATE_ACCEPTED, ClanMembership, Clan, CLAN_NORMAL, CLAN_COLEADER, CLAN_ELDER, CLAN_LEADER, CLAN_RANKS, TYPE_PRIVATE, TYPE_PUBLIC
from .models import ClanLevel, ClanTransaction, TYPE_DEPOSIT, TYPE_UPGRADE, STATE_BAN
from .serializers import ClanMembershipSerializer, ClanSerializer, ClanTransactionSerializer
from game.models import PurchaseWithCoin, AddCoin

logger = logging.getLogger('django')
CLAN_LEADERBOARD_CACHE_TIME = 15 * 60

def calculate_clan_score(memberships):
    score = 0
    for membership in memberships:
        score += membership.member.cup
        score += membership.member.crown * 3
    
    return score


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def list_clans(request):
    member = request.user
    data = request.data

    clans = Clan.objects.filter(type=TYPE_PUBLIC)
    rands = random.choices(clans, k=20)

    return JsonResponse(status=200, data={"clans": ClanSerializer(rands, many=True).data})


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def create_clan(request):
    member = request.user
    data = request.data
    
    if member.clan != None:
        return JsonResponse(status=401, data={"message": "Leave your clan first"})
    
    clan_level1 = ClanLevel.objects.get(level=1)    
    price = clan_level1.price
    if member.coin < price:
        return JsonResponse(status=402, data={"message": "Not enough coins"})
    
    member.coin -= price
    member.save()

    clan = Clan()
    clan.name = data.get("name", "")
    clan.description = data.get("description", "")
    clan.level = 1
    clan.min_cup = data.get("min_cup", "")
    clan.type = data.get("type", TYPE_PUBLIC)
    clan.badge_id = data.get("badge_id", 1)
    clan.save()

    membership = ClanMembership()
    membership.member = member
    membership.clan = clan
    membership.state = STATE_ACCEPTED
    membership.rank = CLAN_LEADER
    membership.save()

    purchase_log = PurchaseWithCoin()
    purchase_log.member = member
    purchase_log.price = price
    purchase_log.forgiving = False
    purchase_log.type = "ClanCreate"
    purchase_log.secondary = str(clan.id)
    purchase_log.success = True
    purchase_log.save()

    return JsonResponse(status=200, data={"message": "success", "clan": ClanSerializer(clan).data})


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def search_clan(request):
    member = request.user
    data = request.data

    name = data.get("name", "")
    clans = Clan.objects.filter(name__icontains=name)[:20]

    return JsonResponse(status=200, data={"clans": ClanSerializer(clans, many=True).data})


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def join_clan(request):
    member = request.user
    data = request.data

    if member.clan != None:
        return JsonResponse(status=401, data={"message": "Leave your clan first"})


    clans = Clan.objects.filter(id=data.get("id", 0))
    if len(clans) == 0:
        return JsonResponse(status=402, data={"message": "Clan Not Found"})
    clan = clans[0]

    clan_levels = ClanLevel.objects.all().order_by("level")
    if clan_levels[clan.level - 1].max_count <= clan.member_count:
        return JsonResponse(status=405, data={"message": "Clan is full"})
    

    memberships = ClanMembership.objects.filter(clan=clan, member=member)
    if len(memberships) > 0:
        return JsonResponse(status=406, data={"message": "Already requested"})
    
    if clan.min_cup > member.cup:
        return JsonResponse(status=407, data={"message": "Don't have minimum cup"})

    joined = False
    membership = ClanMembership()
    membership.member = member
    membership.clan = clan
    if clan.type == TYPE_PUBLIC:
        joined = True
        membership.state = STATE_ACCEPTED
        clan.member_count += 1
        clan.save()
    else:
        joined = False
        membership.state = STATE_INVITE
    membership.rank = CLAN_NORMAL
    membership.save()

    return JsonResponse(status=200, data={"joined": joined})


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def list_clan_members(request):
    member = request.user
    data = request.data

    clans = Clan.objects.filter(id=data.get("id", 0))
    if len(clans) == 0:
        return JsonResponse(status=402, data={"message": "Clan Not Found"})
    clan = clans[0]

    input = data.get("input", "all")
    memberships = []
    if input == "all":
        memberships = ClanMembership.objects.filter(clan=clan).order_by("-rank")
    elif input == "invites":
        memberships = ClanMembership.objects.filter(clan=clan, state=STATE_INVITE).order_by("-member__cup")[:100]
    elif input == "bans":
        memberships = ClanMembership.objects.filter(clan=clan, state=STATE_BAN)
    elif input == "accept":
        memberships = ClanMembership.objects.filter(clan=clan, state=STATE_ACCEPTED).order_by("-rank")
    
    return JsonResponse(status=200, data={"members": ClanMembershipSerializer(memberships, many=True).data})


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def update_clan_membership(request):#Delete or Accept or Reject
    member = request.user
    data = request.data

    if member.clan == None:
        return JsonResponse(status=401, data={"message": "Join a clan first"})

    my_rank = CLAN_RANKS.index(member.clan_membership.rank)

    if my_rank < CLAN_RANKS.index(CLAN_ELDER):
        return JsonResponse(status=402, data={"message": "You don't have access"})
    
    membership = ClanMembership.objects.filter(id=data.get("id", 0))
    if len(membership) == 0:
        return JsonResponse(status=403, data={"message": "Membership not found"})
    membership = membership[0]
    
    input = data.get("input", "accept")
    if input == "accept":
        if membership.member.clan != None:
            return JsonResponse(status=411, data={"message": "Player already has clan!"})
        clan_levels = ClanLevel.objects.all().order_by("level")
        if clan_levels[member.clan.level - 1].max_count <= member.clan.member_count:
            return JsonResponse(status=410, data={"message": "Clan is full"})
        clan = member.clan
        clan.member_count += 1
        clan.save()
        membership.state = STATE_ACCEPTED
        membership.save()
        #TODO: If new member has clan don't join it!!!
        return JsonResponse(status=200, data={"message": "Success"})
    elif input == "unban":
        if membership.state != STATE_BAN:
            return JsonResponse(status=403, data={"message": "Membership not found"})
        membership.delete()
        return JsonResponse(status=200, data={"message": "Success"})
    elif input == "delete":
        if membership.state == STATE_BAN:
            membership.delete()
            return JsonResponse(status=200, data={"message": "Success"})
        elif membership.state == STATE_INVITE:
            membership.state = STATE_BAN
            membership.save()
            return JsonResponse(status=200, data={"message": "Success"})
        else:
            if CLAN_RANKS.index(membership.rank) > my_rank:
                return JsonResponse(status=405, data={"message": "You Cannot fire higher rank"})
            else:
                clan = member.clan
                clan.member_count -= 1
                clan.save()
                membership.state = STATE_BAN
                membership.save()
                return JsonResponse(status=200, data={"message": "Success"})
    elif input == "promote":
        if my_rank < CLAN_RANKS.index(CLAN_COLEADER):
            return JsonResponse(status=406, data={"message": "You don't have access"})
        
        if CLAN_RANKS.index(membership.rank) >= CLAN_RANKS.index(CLAN_COLEADER):
            return JsonResponse(status=407, data={"message": "Maximum level is reached"})
        else:
            membership.rank = CLAN_RANKS[CLAN_RANKS.index(membership.rank) + 1]
            membership.save()
            return JsonResponse(status=200, data={"message": "Success"})
    elif input == "demote":
        if my_rank < CLAN_RANKS.index(CLAN_COLEADER):
            return JsonResponse(status=408, data={"message": "You don't have access"})
        if CLAN_RANKS.index(membership.rank) >= my_rank:
            return JsonResponse(status=409, data={"message": "Cannot demote equal or higher rank"})
        if CLAN_RANKS.index(membership.rank) <= CLAN_RANKS.index(CLAN_NORMAL):
            return JsonResponse(status=410, data={"message": "Minimum Level Reached"})
        membership.rank = CLAN_RANKS[CLAN_RANKS.index(membership.rank) - 1]
        membership.save()
        return JsonResponse(status=200, data={"message": "Success"})


    return JsonResponse(status=420, data={"message": "Invalid input"})


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def update_clan(request):
    member = request.user
    data = request.data
    
    if member.clan == None:
        return JsonResponse(status=401, data={"message": "Join a clan first"})

    my_rank = CLAN_RANKS.index(member.clan_membership.rank)
    if my_rank < CLAN_RANKS.index(CLAN_COLEADER):
        return JsonResponse(status=402, data={"message": "You don't have access"})

    clan = member.clan
    clan.name = data.get("name", "")
    clan.description = data.get("description", "")
    clan.min_cup = data.get("min_cup", "")
    clan.type = data.get("type", TYPE_PUBLIC)
    clan.badge_id = data.get("badge_id", 1)
    clan.save()

    return JsonResponse(status=200, data={"message": "success", "clan": ClanSerializer(clan).data})


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def upgrade_clan(request):
    member = request.user
    data = request.data
    
    if member.clan == None:
        return JsonResponse(status=401, data={"message": "Join a clan first"})

    my_rank = CLAN_RANKS.index(member.clan_membership.rank)
    #if my_rank < CLAN_RANKS.index(CLAN_COLEADER):
    #   return JsonResponse(status=402, data={"message": "You don't have access"})

    clan_levels = ClanLevel.objects.all().order_by("level")
    clan = member.clan
    if clan.level >= len(clan_levels):
        return JsonResponse(status=403, data={"message": "Max Level"})
    
    price = clan_levels[clan.level].price
    if member.coin < price:
        return JsonResponse(status=405, data={"message": "Not enough coins"})
    
    member.coin -= price
    member.save()
    clan.level += 1
    clan.save()
    purchase_log = PurchaseWithCoin()
    purchase_log.member = member
    purchase_log.price = price
    purchase_log.forgiving = False
    purchase_log.type = "ClanUpgrade"
    purchase_log.secondary = str(clan.id)
    purchase_log.success = True
    purchase_log.after_coin = member.coin
    purchase_log.save()
    

    return JsonResponse(status=200, data={"message": "success"})


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def clan_detail(request):
    member = request.user
    data = request.data

    clans = Clan.objects.filter(id=data.get("id", 0))
    if len(clans) == 0:
        return JsonResponse(status=402, data={"message": "Clan Not Found"})
    clan = clans[0]

    input = data.get("input", "+members")
    memberships = []
    score = 0
    if input == "+members":
        memberships = ClanMembership.objects.filter(clan=clan, state=STATE_ACCEPTED).order_by("-rank")
        score = calculate_clan_score(memberships)

    
    return JsonResponse(status=200, data={
        "members": ClanMembershipSerializer(memberships, many=True).data,
        "clan": ClanSerializer(clan).data,
        "score": score,
        })


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def leave_clan(request):
    member = request.user
    data = request.data

    if member.clan == None:
        return JsonResponse(status=401, data={"message": "Join clan first"})


    clan = member.clan

    membership = ClanMembership.objects.filter(clan=clan, member=member)[0]

    clan.member_count -= 1
    clan.save()

    if clan.member_count == 0:
        membership.delete()
        clan.delete()
        return JsonResponse(status=200, data={"message": "success"})


    if membership.rank == CLAN_LEADER:
        selected_membership = None
        rest_memberships = ClanMembership.objects.filter(clan=clan, state=STATE_ACCEPTED, rank=CLAN_COLEADER)
        if len(rest_memberships) > 0:
            selected_membership = rest_memberships[0]
        
        if selected_membership is None:
            rest_memberships = ClanMembership.objects.filter(clan=clan, state=STATE_ACCEPTED, rank=CLAN_ELDER)
            if len(rest_memberships) > 0:
                selected_membership = rest_memberships[0]
        
        if selected_membership is None:
            rest_memberships = ClanMembership.objects.filter(clan=clan, state=STATE_ACCEPTED, rank=CLAN_NORMAL)
            if len(rest_memberships) > 0:
                selected_membership = rest_memberships[0]
        
        selected_membership.rank = CLAN_LEADER
        selected_membership.save()

    membership.delete()

    return JsonResponse(status=200, data={"message": "success"})


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def my_clan(request):
    member = request.user
    data = request.data

    clan = None
    if member.clan != None:
        clan = ClanSerializer(member.clan).data
    
    return JsonResponse(status=200, data={
        "clan": clan,
    })


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def clan_leaderboard(request):
    if cache.get("clan_leaderboard") is None:
        clans = Clan.objects.annotate(
        total_score=Sum(
            F('memberships__member__cup') * 1 + F('memberships__member__crown') * 3 + F('memberships__member__smart') * 5,
            filter=Q(memberships__state=STATE_ACCEPTED)
        )
        ).order_by('-total_score')[:100]
        
        clan_data = []
        for clan in clans:
            clan_info = ClanSerializer(clan).data
            clan_info['total_score'] = clan.total_score
            clan_data.append(clan_info)
        res = {"clans": clan_data}
        cache.set("clan_leaderboard", res, CLAN_LEADERBOARD_CACHE_TIME)
    else:
        res = cache.get("clan_leaderboard")
    return JsonResponse(status=200, data=res)


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def clan_bank_leaderboard(request):
    if cache.get("clan_bank_leaderboard") is None:
        clans = Clan.objects.all().order_by('-bank')[:100]

        res = {"clans": ClanSerializer(clans, many=True).data}
        cache.set("clan_bank_leaderboard", res, CLAN_LEADERBOARD_CACHE_TIME)
    else:
        res = cache.get("clan_bank_leaderboard")
    return JsonResponse(status=200, data=res)
    


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def upgrade_clan_with_bank(request):
    member = request.user
    data = request.data
    
    if member.clan == None:
        return JsonResponse(status=401, data={"message": "Join a clan first"})

    my_rank = CLAN_RANKS.index(member.clan_membership.rank)
    if my_rank < CLAN_RANKS.index(CLAN_COLEADER):
       return JsonResponse(status=402, data={"message": "You don't have access"})


    clan_levels = ClanLevel.objects.all().order_by("level")
    clan = member.clan
    if clan.level >= len(clan_levels):
        return JsonResponse(status=403, data={"message": "Max Level"})


    price = clan_levels[clan.level].price
    if clan.bank < price:
        return JsonResponse(status=405, data={"message": "Not enough coins"})
    
    clan.bank -= price
    clan.level += 1
    clan.save()
    
    transaction = ClanTransaction()
    transaction.member = member
    transaction.type = TYPE_UPGRADE
    transaction.clan = clan
    transaction.amount = price
    transaction.bank = clan.bank
    transaction.member_coin = member.coin
    transaction.save()


    purchase_log = PurchaseWithCoin()
    purchase_log.member = member
    purchase_log.price = price
    purchase_log.forgiving = False
    purchase_log.type = "ClanUpgrade"
    purchase_log.secondary = str(clan.id)
    purchase_log.after_coin = member.coin
    purchase_log.success = True
    purchase_log.save()
    

    return JsonResponse(status=200, data={"message": "success"})


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def deposit_clan_bank(request):
    member = request.user
    data = request.data

    if member.clan == None:
        return JsonResponse(status=401, data={"message": "Join a clan first"})
    
    clan = member.clan
    amount = data["amount"]
    if member.coin < amount:
        return JsonResponse(status=405, data={"message": "Not enough coins"})


    clan.bank += amount
    clan.save()
    member.coin -= amount
    member.save()
    

    transaction = ClanTransaction()
    transaction.member = member
    transaction.type = TYPE_DEPOSIT
    transaction.clan = clan
    transaction.amount = amount
    transaction.bank = clan.bank
    transaction.member_coin = member.coin
    transaction.save()


    purchase_log = PurchaseWithCoin()
    purchase_log.member = member
    purchase_log.price = amount
    purchase_log.forgiving = False
    purchase_log.type = "ClanDeposit"
    purchase_log.secondary = str(clan.id)
    purchase_log.after_coin = member.coin
    purchase_log.success = True
    purchase_log.save()

    return JsonResponse(status=200, data={"message": "success"})


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def list_clan_transactions(request):
    member = request.user
    data = request.data

    if member.clan == None:
        return JsonResponse(status=401, data={"message": "Join a clan first"})
    
    return JsonResponse(status=200, data=
                        {"message": ClanTransactionSerializer(member.clan.transactions.all().order_by("-id")[:100], many=True).data})
