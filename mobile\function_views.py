import logging

from rest_framework import permissions
from rest_framework.decorators import api_view, permission_classes, renderer_classes
from rest_framework_jwt.settings import api_settings
from rest_framework_swagger import renderers
from django.http.response import JsonResponse
from django.db.models import Q, Count

from account.models.member import Member
from account.models.sync_data import SyncD<PERSON>
from .models import STATE_INVITE, STATE_ACCEPTED, Friendship, MobilePhone
from .serializers import FriendShipSerializer
from game.models import PurchaseWithCoin, AddCoin

logger = logging.getLogger('django')


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def list_friends(request):
    member = request.user
    data = request.data

    friendships = Friendship.objects.filter(Q(member1=member) | Q(member2=member)).filter(state=STATE_ACCEPTED)

    return JsonResponse(status=200, data={"friends": FriendShipSerializer(friendships, many=True).data})


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def invite_friend(request):
    member1 = request.user
    data = request.data
    
    input = data.get("input", "___")

    members = Member.objects.filter(Q(username=input) | Q(mobile_number=input) | Q(id=input))
    if len(members) == 0:
        return JsonResponse(status=401, data={"message": "User Not Found"})
    
    member2 = members[0]
    if member1.id == member2.id:
        return JsonResponse(status=401, data={"message": "User Not Found"})


    fr = Friendship.objects.filter((Q(member1=member1) & Q(member2=member2)) | (Q(member2=member1) & Q(member1=member2)))
    if len(fr) > 0:
        return JsonResponse(status=402, data={"message": "Already Invited"})
    
    friendship = Friendship()
    friendship.member1 = member1
    friendship.member2 = member2
    friendship.state = STATE_INVITE
    friendship.save()

    return JsonResponse(status=200, data={"message": "Invite Sent"})


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def change_friendship(request):
    member = request.user
    data = request.data

    friendships = Friendship.objects.filter(Q(member1=member) | Q(member2=member)).filter(id=data.get("id", 0))
    if len(friendships) == 0:
        return JsonResponse(status=400, data={"message": "not found"})

    friendship = friendships[0]
    type = data.get("type", "delete")
    if type == "accept":
        sync = SyncData.objects.get(id=1)
        friendships = Friendship.objects.filter(Q(member1=member) | Q(member2=member)).filter(state=STATE_ACCEPTED)
        if len(friendships) >= sync.max_friends_count:
            return JsonResponse(status=401, data={"message": "Max Friendship reached"})
        friendship.state = STATE_ACCEPTED
        friendship.save()

    elif type == "delete":
        friendship.delete()
    
    return JsonResponse(status=200, data={"message": "success"})


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def list_invites(request):
    member = request.user

    member = request.user
    data = request.data

    friendships = Friendship.objects.filter(member2=member).filter(state=STATE_INVITE)

    return JsonResponse(status=200, data={"friends": FriendShipSerializer(friendships, many=True).data})


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def unlock_mobile(request):
    member = request.user
    data = request.data

    mobiles = MobilePhone.objects.filter(id=data.get("id", -1))
    if len(mobiles) == 0:
        return JsonResponse(status=401, data={"message": "Mobile not found"})
    mobile = mobiles[0]
    if mobile.price > member.coin:
        return JsonResponse(status=402, data={"message": "Not enough coins"})
    
    member.coin -= mobile.price
    member.save()

    purchase_log = PurchaseWithCoin()
    purchase_log.member = member
    purchase_log.price = mobile.price
    purchase_log.forgiving = False
    purchase_log.type = "MobilePhone"
    purchase_log.secondary = mobile.name
    purchase_log.success = True
    purchase_log.after_coin = member.coin
    purchase_log.save()
    
    return JsonResponse(status=200, data={"message": "success"})
