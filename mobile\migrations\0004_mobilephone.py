# Generated by Django 2.2.28 on 2024-12-26 00:30

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mobile', '0003_auto_20241224_2055'),
    ]

    operations = [
        migrations.CreateModel(
            name='MobilePhone',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('removed', models.DateTimeField(blank=True, default=None, editable=False, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('last_update', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(default='', max_length=100)),
                ('motto', models.CharField(default='', max_length=200)),
                ('price', models.IntegerField(default=0)),
                ('scene_path', models.CharField(blank=True, default='', max_length=200)),
                ('is_test', models.<PERSON><PERSON>an<PERSON>ield(default=False)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
