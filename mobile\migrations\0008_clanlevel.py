# Generated by Django 2.2.28 on 2025-01-01 22:50

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mobile', '0007_clanmembership_rank'),
    ]

    operations = [
        migrations.CreateModel(
            name='ClanLevel',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('removed', models.DateTimeField(blank=True, default=None, editable=False, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('last_update', models.DateTimeField(auto_now=True)),
                ('level', models.IntegerField(default=1)),
                ('max_count', models.IntegerField(default=5)),
                ('price', models.IntegerField(default=5)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
