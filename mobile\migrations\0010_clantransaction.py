# Generated by Django 2.2.28 on 2025-01-20 12:54

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('mobile', '0009_auto_20250119_2306'),
    ]

    operations = [
        migrations.CreateModel(
            name='ClanTransaction',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('removed', models.DateTimeField(blank=True, default=None, editable=False, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('last_update', models.DateTimeField(auto_now=True)),
                ('type', models.CharField(choices=[('Private', 'Private'), ('Public', 'Public')], default='Deposit', max_length=100)),
                ('amount', models.IntegerField(default=0)),
                ('bank', models.IntegerField(default=0)),
                ('member_coin', models.IntegerField(default=0)),
                ('clan', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transactions', to='mobile.Clan')),
                ('member', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='clantransactionmembership', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
