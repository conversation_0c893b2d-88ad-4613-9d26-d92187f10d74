import json
from django.db import models
from django.utils import timezone
from datetime import datetime, timedelta
from utils.models import BaseModel


STATE_INVITE = "Invite"
STATE_ACCEPTED = "Accepted"
STATE_BAN = "Ban"

FRIENDSHIP_CHOICES = (
    (STATE_INVITE, STATE_INVITE),
    (STATE_ACCEPTED, STATE_ACCEPTED),
    (STATE_BAN, STATE_BAN),
)

class MobilePhone(BaseModel):
    name = models.CharField(max_length=100, default='')
    motto = models.CharField(max_length=200, default='')
    price = models.IntegerField(default=0)
    scene_path = models.CharField(max_length=200, default='', blank=True)
    is_test = models.BooleanField(default=False)


class Friendship(BaseModel):
    member1 = models.ForeignKey('account.Member', related_name='m1', on_delete=models.CASCADE)
    member2 = models.ForeignKey('account.Member', related_name='m2', on_delete=models.CASCADE)
    state = models.CharField(max_length=100, choices=FRIENDSHIP_CHOICES, blank=True)
    member1_contact = models.CharField(max_length=100, default='', blank=True)
    member2_contact = models.CharField(max_length=100, default='', blank=True)

    @property
    def member1_online(self):
        return self.member1.is_online

    @property
    def member2_online(self):
        return self.member2.is_online
    
    @property
    def member1_presence(self):
        return self.member1.presence
    
    @property
    def member2_presence(self):
        return self.member2.presence
    
    @property
    def member1_presence_data(self):
        return self.member1.presence_data
    
    @property
    def member2_presence_data(self):
        return self.member2.presence_data


    @property
    def member1_handle(self):
        return self.member1.handle


    @property
    def member1_username(self):
        return self.member1.username
    

    @property
    def member1_mobile_number(self):
        return self.member1.mobile_number


    @property
    def member2_handle(self):
        return self.member2.handle


    @property
    def member2_username(self):
        return self.member2.username
    

    @property
    def member2_mobile_number(self):
        return self.member2.mobile_number


TYPE_PRIVATE = "Private"
TYPE_PUBLIC = "Public"

CLAN_TYPE_CHOICES = (
    (TYPE_PRIVATE, TYPE_PRIVATE),
    (TYPE_PUBLIC, TYPE_PUBLIC),
)
class Clan(BaseModel):
    name = models.CharField(max_length=100, default='', blank=True)
    description = models.TextField(default='', blank=True)
    level = models.IntegerField(default=1)
    member_count = models.IntegerField(default=1)
    min_cup = models.IntegerField(default=0)
    type = models.CharField(max_length=100, choices=CLAN_TYPE_CHOICES, default=TYPE_PUBLIC)
    bank = models.IntegerField(default=0)
    badge_id = models.IntegerField(default=1)

    def __str__(self):
        return "(" + str(self.id) + ") " + self.name


CLAN_LEADER = "Leader"
CLAN_COLEADER = "Coleader"
CLAN_ELDER = "Elder"
CLAN_NORMAL = "Normal"

CLANSHIP_CHOICES = (
    (CLAN_LEADER, CLAN_LEADER),
    (CLAN_COLEADER, CLAN_COLEADER),
    (CLAN_ELDER, CLAN_ELDER),
    (CLAN_NORMAL, CLAN_NORMAL),
)
CLAN_RANKS = [CLAN_NORMAL, CLAN_ELDER, CLAN_COLEADER, CLAN_LEADER]
class ClanMembership(BaseModel):
    member = models.ForeignKey('account.Member', related_name='clanmembership', on_delete=models.CASCADE)
    clan = models.ForeignKey(Clan, related_name='memberships', on_delete=models.CASCADE)
    state = models.CharField(max_length=100, choices=FRIENDSHIP_CHOICES, default=STATE_ACCEPTED, blank=True)
    rank = models.CharField(max_length=100, choices=CLANSHIP_CHOICES, default=CLAN_NORMAL, blank=True)

    @property 
    def username(self):
        return self.member.username

    @property 
    def handle(self):
        return self.member.handle

    @property 
    def cup(self):
        return self.member.cup
    
    @property 
    def crown(self):
        return self.member.crown

    @property
    def title(self):
        return self.member.title
    
    @property 
    def player_id(self):
        return self.member.id


class ClanLevel(BaseModel):
    level = models.IntegerField(default=1)
    max_count = models.IntegerField(default=5)
    price = models.IntegerField(default=5)


TYPE_UPGRADE = "Upgrade"
TYPE_DEPOSIT = "Deposit"

CLAN_TRANSACTION_CHOICES = (
    (TYPE_UPGRADE, TYPE_UPGRADE),
    (TYPE_DEPOSIT, TYPE_DEPOSIT),
)
class ClanTransaction(BaseModel):
    member = models.ForeignKey('account.Member', related_name='clantransactionmembership', on_delete=models.CASCADE)
    clan = models.ForeignKey(Clan, related_name='transactions', on_delete=models.CASCADE)
    type = models.CharField(max_length=100, default=TYPE_DEPOSIT, choices=CLAN_TRANSACTION_CHOICES)
    amount = models.IntegerField(default=0)
    bank = models.IntegerField(default=0)
    member_coin = models.IntegerField(default=0)

    @property 
    def handle(self):
        return self.member.handle
