from rest_framework import serializers
from .models import Friendship, MobilePhone, Clan, ClanMembership, ClanLevel, ClanTransaction


class FriendShipSerializer(serializers.ModelSerializer):
    class Meta:
        model = Friendship
        fields = ("id", 'state', 'member1_online', 'member2_online', 'member1_presence', 'member2_presence', 
                  'member1_presence_data', 'member2_presence_data', 'member1_handle', 'member2_handle',
                  'member1_contact', 'member2_contact', 'member1_username', 'member2_username')


class ClanSerializer(serializers.ModelSerializer):
    class Meta:
        model = Clan
        fields = ("id", 'name', 'description', 'level', 'member_count', 'min_cup', 'type', 'bank', 'badge_id')


class ClanLevelSerializer(serializers.ModelSerializer):
    class Meta:
        model = ClanLevel
        fields = ("id", 'level', 'max_count', 'price')


class ClanMembershipSerializer(serializers.ModelSerializer):
    class Meta:
        model = ClanMembership
        fields = ("id", 'handle', 'cup', 'crown', 'title', 'username', 'rank', 'state', 'player_id')


class MobilePhoneSerializer(serializers.ModelSerializer):
    class Meta:
        model = MobilePhone
        fields = ("id", "name", "motto", "price", "scene_path", "is_test")


class ClanTransactionSerializer(serializers.ModelSerializer):
    class Meta:
        model = ClanTransaction
        fields = ("id", 'handle', 'amount', 'type')
