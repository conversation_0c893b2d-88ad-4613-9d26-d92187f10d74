from django.urls import path
from .function_views import unlock_mobile, invite_friend, list_invites, list_friends, change_friendship
from .clan_function_views import list_clans, create_clan, search_clan, join_clan, list_clan_members, update_clan_membership
from .clan_function_views import update_clan, upgrade_clan, clan_detail, leave_clan, my_clan, clan_leaderboard
from .clan_function_views import upgrade_clan_with_bank, deposit_clan_bank, list_clan_transactions, clan_bank_leaderboard


urlpatterns = [
    path(r'invite_friend/', invite_friend, name="invite_friend"),
    path(r'list_invites/', list_invites, name="list_invites"),
    path(r'list_friends/', list_friends, name="list_friends"),
    path(r'change_friendship/', change_friendship, name="change_friendship"),
    
    path(r'purchase_mobile/', unlock_mobile, name="unlock_mobile"),

    #Clans
    path(r'list_clans/', list_clans, name="list_clans"),
    path(r'create_clan/', create_clan, name="create_clan"),
    path(r'search_clan/', search_clan, name="search_clan"),
    path(r'join_clan/', join_clan, name="join_clan"),
    path(r'list_clan_members/', list_clan_members, name="list_clan_members"),
    path(r'update_clan_membership/', update_clan_membership, name="update_clan_membership"),
    path(r'update_clan/', update_clan, name="update_clan"),
    path(r'upgrade_clan/', upgrade_clan, name="upgrade_clan"),
    path(r'clan_detail/', clan_detail, name="clan_detail"),
    path(r'leave_clan/', leave_clan, name="leave_clan"),
    path(r'my_clan/', my_clan, name="my_clan"),
    path(r'clan_leaderboard/', clan_leaderboard, name="clan_leaderboard"),
    path(r'clan_bank_leaderboard/', clan_bank_leaderboard, name="clan_bank_leaderboard"),
    path(r'upgrade_clan_with_bank/', upgrade_clan_with_bank, name="upgrade_clan_with_bank"),
    path(r'deposit_clan_bank/', deposit_clan_bank, name="deposit_clan_bank"),
    path(r'list_clan_transactions/', list_clan_transactions, name="list_clan_transactions"),
]
