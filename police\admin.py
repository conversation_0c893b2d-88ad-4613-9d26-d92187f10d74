import json
from django.contrib import admin
from utils.admin import BaseAdmin
from .models import PoliceCall, PoliceReport, Crime, Arrest, JailExit, Kill, JobQueueMember, PoliceLaw
from game.models import JobDone

class PoliceCallAdmin(BaseAdmin):
    list_display = ('id', 'created', 'caller', 'caller_handle', 'crime', 'guilty', 'guilty_handle', 'location', 'server', 'resolved', 'text')
    list_editable = ('resolved',)
    raw_id_fields = ('caller', 'guilty')
    list_filter = ('crime', 'location', 'server')
    search_fields = ('caller__id', 'caller__handle', 'caller__username', 'guilty__id', 'guilty__handle', 'guilty__username')


class PoliceReportAdmin(BaseAdmin):
    list_display = ('id', 'created', 'reporter', 'reporter_handle', 'crime', 'guilty', 'guilty_handle', 'success', 'text', 'jail_time', 'ban_time')
    list_editable = ()
    raw_id_fields = ('reporter', 'guilty', 'arrest', 'call', 'server_instance')
    list_filter = ('crime', 'server', 'success')
    search_fields = ('reporter__id', 'reporter__handle', 'reporter__username', 'guilty__id', 'guilty__handle', 'guilty__username')


class CrimeAdmin(BaseAdmin):
    list_display = ('id', 'created', 'member', 'guilty_handle', 'type', 'is_jail', 'police_report', 'jail_time', 'ban_time')
    list_editable = ()
    raw_id_fields = ('member',)
    list_filter = ('type', 'is_jail', 'police_report')


class ArrestAdmin(BaseAdmin):
    list_display = ('id', 'created', 'police', 'police_handle', 'guilty', 'guilty_handle', 'report')
    list_editable = ()
    raw_id_fields = ('guilty', 'police')
    list_filter = ('created',)
    search_fields = ('police__id', 'police__handle', 'police__username', 'guilty__id', 'guilty__handle', 'guilty__username')


class JailExitAdmin(BaseAdmin):
    list_display = ('id', 'created', 'guilty', 'guilty_handle')
    list_editable = ()
    raw_id_fields = ('guilty',)
    list_filter = ('created',)
    search_fields = ('guilty__id', 'guilty__handle', 'guilty__username')


class KillAdmin(BaseAdmin):
    list_display = ('id', 'created', 'killer_handle', 'killer', 'dead_handle', 'dead')
    list_editable = ()
    raw_id_fields = ()
    list_filter = ('created',)
    search_fields = ('killer__id', 'killer__handle', 'killer__username', 'dead__id', 'dead__handle', 'dead__username')


def cancel_from_q(modeladmin, request, queryset):
    for q_item in queryset:
        q_item.cancel()
cancel_from_q.short_description = "Cancel selected from Q"

def pop_from_q(modeladmin, request, queryset):
    for q_item in queryset:
        q_item.pop()
pop_from_q.short_description = "Pop selected from Q"

def clear_job_done(member, job):
    JobDone.objects.filter(member=member, job=job).delete()

def doctor_ban(modeladmin, request, queryset):
    for item in queryset:
        item.member.add_ban(7 * 3600 * 24, True)
        clear_job_done(member=item.member, job="heal")
        clear_job_done(member=item.member, job="visit")
        item.member.title = ""
        item.member.save()
        member_data = item.member.data.all()[0]
        data = member_data.data
        data["arm"] = None
        if "job_stats" in data:
            for key in data["job_stats"]:
                data["job_stats"][key] = 0
        member_data.data_string = json.dumps(data)
        member_data.save()
        item.cancel()
doctor_ban.short_description = "DoctorBan: Cancel Q item, 7d Ban User, zero the jobdones"


class JobQAdmin(BaseAdmin):
    list_display = ('id', 'created', 'member', 'member_handle', 'score', 'title', 'purchase_count', 'popped', 'cancelled')
    list_editable = ()
    raw_id_fields = ('member',)
    list_filter = ('created', 'popped', 'cancelled')
    search_fields = ('member__id', 'member__handle', 'member__username')
    actions = [cancel_from_q, pop_from_q, doctor_ban]


class PoliceLawAdmin(BaseAdmin):
    list_display = ('id', 'last_update', 'law')
    list_editable = ()


admin.site.register(PoliceCall, PoliceCallAdmin)
admin.site.register(PoliceReport, PoliceReportAdmin)
admin.site.register(Crime, CrimeAdmin)
admin.site.register(Arrest, ArrestAdmin)
admin.site.register(JailExit, JailExitAdmin)
admin.site.register(Kill, KillAdmin)
admin.site.register(JobQueueMember, JobQAdmin)
admin.site.register(PoliceLaw, PoliceLawAdmin)
