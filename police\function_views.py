import logging

from rest_framework import permissions
from rest_framework.decorators import api_view, permission_classes, renderer_classes
from rest_framework_jwt.settings import api_settings
from rest_framework_swagger import renderers
from django.http.response import JsonResponse
from django.utils import timezone
from datetime import <PERSON><PERSON><PERSON>

from account.models.member import MARKET_GOOGLEPLAY, Member
from account.models.sync_data import SyncData
from game.models import P<PERSON>chaseWithCoin, GameServer
from .models import PoliceCall, PoliceReport, Crime, Arrest, JailExit, Kill, JobQueueMember, CRIME_FAULT, POLICE_TITLES, PoliceLaw
from .models import <PERSON>OR<PERSON>OHBAN, SARVAN, POLICE_TIMES, OSTOVAR
from game.models import JobDone
from .serailizers import Police<PERSON>allSerializer, CrimeSerializer
from utils.utils import get_country_from_request

logger = logging.getLogger('django')


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def police_call(request):
    member = request.user
    data = request.data

    if member.cup < 30:
        return JsonResponse(status=400, data={"message": "low cup"})

    call = PoliceCall()
    call.caller = member
    call.crime = data.get("crime", "")
    if data.get("guilty", None) != None:
        call.guilty_id = data.get("guilty", 1)
    call.text = data.get("text", "")
    call.location = data.get("location", "")
    call.server = data.get("server", "")
    call.save()

    return JsonResponse(status=200, data={"message": "success"})


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def police_report(request):
    member:Member = request.user
    data = request.data

    if member.is_ban:
        return JsonResponse(status=400, data={"message": "You are not police!"})


    if not (member.title in POLICE_TITLES and member.police_job_enabled):
        return JsonResponse(status=400, data={"message": "You are not police!"})

    
    JOB_DONE_THRESHOLD = 20
    TIME_DELTA = timedelta(hours=1)
    BAN_TIME = 3600 * 24 * 7
    COIN_PENALTY = 10000
    
    count = JobDone.objects.filter(member=member, job="policeReport").filter(created__gt=timezone.now() - TIME_DELTA).count()
    if count > JOB_DONE_THRESHOLD:
        penalty = COIN_PENALTY
        member.coin -= penalty
        if member.coin < 0:
            member.coin = 0
        member.save()
        member.add_ban(BAN_TIME, True)

        return JsonResponse(status=400, data={"message": "HaHa"})

    count = PoliceReport.objects.filter(reporter=member, guilty__id=data.get("guilty", 1), created__gt=timezone.now() - timedelta(seconds=120)).count()
    if count > 0:
        return JsonResponse(status=400, data={"message": "HaHa"})
    
    report = PoliceReport()
    report.reporter = member
    report.crime = data.get("crime", "")
    if data.get("guilty", None) != None:
        report.guilty_id = data.get("guilty", 1)
    report.jail_time = data.get("jail_time", 60)
    report.ban_time = data.get("ban_time", 60)
    report.text = data.get("text", "")
    report.server = data.get("server", "")

    rank = POLICE_TITLES.index(member.title)
    report.jail_time = min(report.jail_time, POLICE_TIMES[rank])
    report.ban_time = min(report.ban_time, POLICE_TIMES[rank])

    report.save()

    crime = Crime()
    crime.member = report.guilty
    crime.type = report.crime
    crime.jail_time = report.jail_time
    crime.ban_time = report.ban_time

    server = GameServer.objects.get(id=report.server)
    is_private = server.is_private
    report.server_instance = server


    job_done = False
    arrest = None
    if data.get("arrest_id", None) != None:
        arrest = Arrest.objects.get(id=data.get("arrest_id"))
        arrest.report = report
        arrest.save()
        report.guilty = arrest.guilty
        report.arrest = arrest
        report.save()
        if report.guilty.in_prison:
            job_done = True
            report.success = True
            report.guilty.add_jail(report.jail_time)
            if not is_private:
                report.guilty.add_ban(report.ban_time, False)
            else:
                report.success = False
            crime.is_jail = True
    

    call = None
    if data.get("police_call_id", None) != None:
        call = PoliceCall.objects.get(id=data.get("police_call_id"))
        call.resolved = True
        call.save()
        if report.guilty is None:
            report.guilty = call.guilty
        report.call = call
        report.save()
        #job_done = True
    

    if report.crime == CRIME_FAULT:
        job_done = False
        if report.guilty:
            report.guilty.add_jail(-report.jail_time - 2 * 120)
            report.guilty.add_ban(-report.ban_time, False)
        report.jail_time = 0
        report.ban_time = 0
        crime.is_jail = False

    report.save()
    crime.police_report = report
    crime.save()

    if is_private:
        job_done = False
    else:
        #Check For SOO estefade
        if report.guilty and not report.crime == CRIME_FAULT:
            if report.guilty.title in POLICE_TITLES:
                if member.title == GOOROOHBAN or member.title == SARVAN or member.title == OSTOVAR:
                    job_done = False
                    report.guilty.add_jail(-report.jail_time - 2 * 120)
                    report.guilty.add_ban(-report.ban_time, False)
                    report.jail_time = 0
                    report.ban_time = 0
                    report.save()
                    member.add_ban(12 * 3600, False)
                else:
                    rank1 = POLICE_TITLES.index(member.title)
                    rank2 = POLICE_TITLES.index(report.guilty.title)
                    if rank1 <= rank2:
                        job_done = False
                        report.guilty.add_jail(-report.jail_time - 2 * 120)
                        report.guilty.add_ban(-report.ban_time, False)
                        report.jail_time = 0
                        report.ban_time = 0
                        report.save()
                        member.add_ban(24 * 3600, False)
        #Check For SOO estefade

    return JsonResponse(status=200, data={
        "message": "Success",
        "job_done": job_done
        })


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def police_arrest(request):
    member = request.user
    data = request.data

    # return JsonResponse(status=200, data={"message": "You are not police!"})

    if member.full_ban or member.is_ban:
        logger.info("police arrest: (is ban)" + member.username)
        return JsonResponse(status=400, data={"message": "You are not police!"})

    police_titles = POLICE_TITLES
    if not member.title in police_titles:
        logger.info("police arrest: (not police)" + member.username)
        return JsonResponse(status=400, data={"message": "You are not police!"})

    count = 0
    for g in data.get("guilties", []):
        count = Arrest.objects.filter(police=member, guilty__id=g, created__gt=timezone.now() - timedelta(seconds=120)).count()
        if count > 0:
            continue
        arrest = Arrest()
        arrest.police = member
        arrest.guilty_id = g
        arrest.save()
        # logger.info("police arrest this player: " + str(g))
        # logger.info(arrest.guilty)
        arrest.guilty.in_prison = True
        arrest.guilty.add_jail(2 * 60)
        logger.info(arrest.guilty.in_prison)
        count += 1

    return JsonResponse(status=200, data={"arrest": count})


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def jail_exit(request):
    member = request.user
    if member.is_jail:
        return JsonResponse(status=400, data={"message": "You Should Stay in Jail!"})
    if member.in_prison == False:
        return JsonResponse(status=200, data={"message": "Enjoy Freedom!"})

    member.in_prison = False
    member.save()
    exit = JailExit()
    exit.guilty = member
    exit.save()
    return JsonResponse(status=200, data={"message": "Enjoy Freedom!"})


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def police_call_list(request):
    data = request.data
    server = data.get("server", None)
    if server is None:
        calls = PoliceCall.objects.filter(resolved=False).order_by('-created')[:20]
    else:
        game_server = GameServer.objects.filter(id=int(server))[0]
        calls = PoliceCall.objects.filter(resolved=False, server=game_server.name).order_by('-created')[:20]

    return JsonResponse(status=200, data={"calls": PoliceCallSerializer(calls, many=True).data})


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def jail_stat(request):
    member = request.user
    

    return JsonResponse(status=200, data=
                        {
                            "jail_time": member.jail_time,
                        })


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def jail_reduce(request):
    member = request.user
    data = request.data

    price = data["price"]
    reduce = data["reduce"]

    if member.coin < price:
        return JsonResponse(status=400, data={"message": "Not Enough Coin"})
    
    purchase_log = PurchaseWithCoin()
    purchase_log.member = member
    purchase_log.price = price
    purchase_log.forgiving = False
    purchase_log.success = True
    purchase_log.type = "JailReduce"
    purchase_log.secondary = reduce
    purchase_log.success = True
    purchase_log.save()

    member.coin -= price
    member.add_jail(-reduce)#Also saves

    return JsonResponse(status=200, data=
                        {
                            "coin": member.coin,
                            "jail_time": member.jail_time,
                        })


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def arrest_list(request):
    member = request.user

    arrests = Arrest.objects.filter(report=None).filter(police=member).order_by('-created')[:20]
    results = []
    for arr in arrests:
        results.append({
            "arrest_id": arr.id,
            "handle": arr.guilty.handle,
            "time": arr.created
        })

    return JsonResponse(status=200, data={"results": results})


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def i_killed(request):
    member = request.user
    data = request.data

    kill = Kill()
    kill.killer = member
    kill.dead_id = data.get("dead_id", 1)
    kill.save()

    return JsonResponse(status=200, data={"results": "success"})


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def job_q_stats(request):
    member = request.user
    data = request.data

    type = data.get("type", "پلیس")


    if type == "پلیس":
        if member.police_job_enabled:
            return JsonResponse(status=200, data={
                "has_q": False,
                "rank": 100 * 1000,
                "has_license": True,
            })


    my_q = JobQueueMember.objects.filter(cancelled=False, popped=False, type=type, member=member)
    if len(my_q) == 0:
        return JsonResponse(status=200, data={
                "has_q": False,
                "rank": 100 * 1000,
                "has_license": False,
            })
    
    my_q = my_q[0]
    rank = JobQueueMember.objects.filter(cancelled=False, popped=False, type=type, score__gte=my_q.score).count()
    time = rank / 10 + 1

    sync_data = SyncData.objects.get(id=1)
    gateway_type = "IAP"
    if member.market == MARKET_GOOGLEPLAY:
        if sync_data.shop_check_ip:
            country = get_country_from_request(request)
            logger.info("Checking Ip For Shop List: " + country)
            if country == "IR":
                gateway_type = "IPG"
            else:
                gateway_type = "IAP"
        else:
            gateway_type = "IPG"
        

        if member.force_iap:
            gateway_type = "IAP"
        if member.force_ipg:
            gateway_type = "IPG"
    


    return JsonResponse(status=200, data={
                "has_q": True,
                "rank": rank,
                "time": time,
                "has_license": False,
                "score": my_q.score,
                "gateway_type": gateway_type,
            })


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def add_me_to_job_q(request):
    member = request.user
    data = request.data

    type = data.get("type", "پلیس")
    my_q = JobQueueMember.objects.filter(cancelled=False, popped=False, type=type, member=member)
    if len(my_q) > 0:
        return JsonResponse(status=400, data={"message": "Already in Q"})
    
    price = data.get("price", 100)
    if member.coin < price:
        return JsonResponse(status=401, data={"message": "Not Enough Coin"})
    
    member.coin -= price
    member.save()

    q_item = JobQueueMember()
    q_item.member = member
    q_item.type = type
    q_item.score = 0
    q_item.save()

    purchase_log = PurchaseWithCoin()
    purchase_log.member = member
    purchase_log.price = price
    purchase_log.forgiving = False
    purchase_log.success = True
    purchase_log.type = "JobQ"
    purchase_log.secondary = type
    purchase_log.save()

    return JsonResponse(status=200, data={"message": "Success"})


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def add_score_to_job_q(request):
    member = request.user
    data = request.data

    type = data.get("type", "پلیس")

    my_q = JobQueueMember.objects.filter(cancelled=False, popped=False, type=type, member=member)
    if len(my_q) == 0:
        return JsonResponse(status=400, data={"message": "You are not in Q"})
    
    coins = data.get("coin", 0)

    if member.coin < coins:
        return JsonResponse(status=401, data={"message": "Not Enough Coin"})


    my_q = my_q[0]
    my_q.score += coins
    my_q.save()

    member.coin -= coins
    member.save()

    purchase_log = PurchaseWithCoin()
    purchase_log.member = member
    purchase_log.price = coins
    purchase_log.forgiving = False
    purchase_log.success = True
    purchase_log.type = "PoliceQScore"
    purchase_log.secondary = coins
    purchase_log.after_coin = member.coin
    purchase_log.save()

    return JsonResponse(status=200, data={
                "coin": member.coin,
            })


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def police_law(request):
    law = PoliceLaw.objects.get(id=1)
    
    return JsonResponse(status=200, data={
        "law": law.law,
        "last_update": law.last_update,
    })



#Gameserver calls this to check the damage!
@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def can_damage(request):
    data = request.data

    id = data.get("backend_id", "")
    mems = Member.objects.filter(id=id)

    if len(mems) == 0:
        return JsonResponse(status=400, data={"message": "Invalid Member"})
    
    member = mems[0]
    if not member.title in POLICE_TITLES:
        return JsonResponse(status=401, data={"message": "You are not police!"})

    
    return JsonResponse(status=200, data={})
