# Generated by Django 2.2.28 on 2024-10-13 05:52

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PoliceCall',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('removed', models.DateTimeField(blank=True, default=None, editable=False, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('last_update', models.DateTimeField(auto_now=True)),
                ('crime', models.CharField(choices=[('بددهنی', 'بددهنی'), ('مزاحمت', 'مزاحمت'), ('کم کاری', 'کم کاری'), ('دزدی', 'دزدی')], default='', max_length=100)),
                ('caller', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='policecaller', to=settings.AUTH_USER_MODEL)),
                ('guilty', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='policecallsguilty', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
