# Generated by Django 2.2.28 on 2024-10-13 08:11

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('police', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='policecall',
            name='location',
            field=models.CharField(default='', max_length=100),
        ),
        migrations.AddField(
            model_name='policecall',
            name='text',
            field=models.TextField(default=''),
        ),
        migrations.CreateModel(
            name='PoliceReport',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('removed', models.DateTimeField(blank=True, default=None, editable=False, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('last_update', models.DateTimeField(auto_now=True)),
                ('crime', models.CharField(choices=[('بددهنی', 'بددهنی'), ('مزاحمت', 'مزاحمت'), ('کم کاری', 'کم کاری'), ('دزدی', 'دزدی')], default='', max_length=100)),
                ('jail_time', models.IntegerField(default=60)),
                ('ban_time', models.IntegerField(default=60)),
                ('guilty', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='reportguilties', to=settings.AUTH_USER_MODEL)),
                ('reporter', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='policereports', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Crime',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('removed', models.DateTimeField(blank=True, default=None, editable=False, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('last_update', models.DateTimeField(auto_now=True)),
                ('type', models.CharField(choices=[('بددهنی', 'بددهنی'), ('مزاحمت', 'مزاحمت'), ('کم کاری', 'کم کاری'), ('دزدی', 'دزدی')], default='', max_length=100)),
                ('is_jail', models.BooleanField(default=False)),
                ('ban_time', models.IntegerField(default=0)),
                ('member', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='crimes', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
