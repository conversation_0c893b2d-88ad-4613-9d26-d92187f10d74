# Generated by Django 2.2.28 on 2024-10-13 10:06

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('police', '0005_crime_police_report'),
    ]

    operations = [
        migrations.AlterField(
            model_name='crime',
            name='type',
            field=models.CharField(choices=[('حرف بد', 'حرف بد'), ('مزاحمت', 'مزاحمت'), ('کم کاری', 'کم کاری'), ('دزدی', 'دزدی')], default='', max_length=100),
        ),
        migrations.AlterField(
            model_name='policecall',
            name='crime',
            field=models.CharField(choices=[('حرف بد', 'حرف بد'), ('مزاحمت', 'مزاحمت'), ('کم کاری', 'کم کاری'), ('دزدی', 'دزدی')], default='', max_length=100),
        ),
        migrations.AlterField(
            model_name='policereport',
            name='crime',
            field=models.CharField(choices=[('حرف بد', 'حرف بد'), ('مزاحمت', 'مزاحمت'), ('کم کاری', 'کم کاری'), ('دزدی', 'دزدی')], default='', max_length=100),
        ),
        migrations.CreateModel(
            name='JailExit',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('removed', models.DateTimeField(blank=True, default=None, editable=False, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('last_update', models.DateTimeField(auto_now=True)),
                ('guilty', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='jailexits', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Arrest',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('removed', models.DateTimeField(blank=True, default=None, editable=False, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('last_update', models.DateTimeField(auto_now=True)),
                ('guilty', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='guiltyarrests', to=settings.AUTH_USER_MODEL)),
                ('police', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='arrests', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
