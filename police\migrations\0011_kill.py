# Generated by Django 2.2.28 on 2024-10-20 10:59

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('police', '0010_crime_jail_time'),
    ]

    operations = [
        migrations.CreateModel(
            name='Kill',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('removed', models.DateTimeField(blank=True, default=None, editable=False, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('last_update', models.DateTimeField(auto_now=True)),
                ('dead', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='deads', to=settings.AUTH_USER_MODEL)),
                ('killer', models.Foreign<PERSON>ey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='killers', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
