# Generated by Django 2.2.28 on 2024-10-29 21:45

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('police', '0011_kill'),
    ]

    operations = [
        migrations.CreateModel(
            name='JobQueueMember',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('removed', models.DateTimeField(blank=True, default=None, editable=False, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('last_update', models.DateTimeField(auto_now=True)),
                ('type', models.CharField(blank=True, choices=[('پلیس', 'پلیس')], default='پلیس', max_length=100)),
                ('cancelled', models.<PERSON><PERSON>anField(default=False)),
                ('Member', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='JobQs', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
