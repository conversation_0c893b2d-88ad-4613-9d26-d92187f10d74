# Generated by Django 2.2.28 on 2024-12-16 22:39

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('police', '0016_jobqueuemember_score'),
    ]

    operations = [
        migrations.CreateModel(
            name='PoliceLaw',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('removed', models.DateTimeField(blank=True, default=None, editable=False, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('law', models.TextField(blank=True, default='')),
                ('last_update', models.DateTimeField()),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
