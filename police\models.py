import json
from django.db import models
from django.utils import timezone
from datetime import datetime, timedelta
from utils.models import BaseModel
from shop.models import Purchase


GOOROOHBAN = "گروهبان"
OSTOVAR = "استوار"
SARVAN = "سروان"
SARGORD = "سرگرد"
SARHANG = "سرهنگ"
SARDAR = "سردار"
SARLASHKAR = "امیر سرلشکر"
BAZRAS = "رییس بازرسی"
PRESIDENT = "رییس جمهور"
POLICE_TITLES = ['پلیس', GOOROOHBAN, OSTOVAR, SARVAN, SARGORD, SARHANG, SARDAR, SARLASHKAR, BAZRAS, PRESIDENT]
POLICE_TIMES = [0, 5 * 60, 10 * 60, 15 * 60, 20 * 60, 60 * 60, 60 * 60, 60 * 60, 60 * 60, 60 * 60]


DOCTOR_TITLES = ['دکتر', 'پرستار', 'مدیر بخش', 'رییس بیمارستان', 'سرپرستار']


CRIME_FAULT = "دستگیری اشتباه"
CRIME_SWEAR = "حرف بد"
CRIME_DISTURBANCE = "مزاحمت"
CRIME_JOB = "کم کاری"
CRIME_THIEF = "دزدی"
CRIME_SOESTEFADE = "سواستفاده"


CRIME_CHOICES = (
    (CRIME_SWEAR, CRIME_SWEAR),
    (CRIME_DISTURBANCE, CRIME_DISTURBANCE),
    (CRIME_JOB, CRIME_JOB),
    (CRIME_THIEF, CRIME_THIEF),
    (CRIME_FAULT, CRIME_FAULT),
)


class PoliceCall(BaseModel):
    caller = models.ForeignKey("account.Member", related_name='policecaller', on_delete=models.CASCADE, null=True)
    crime = models.CharField(max_length=100, default='', choices=CRIME_CHOICES)
    guilty = models.ForeignKey("account.Member", related_name='policecallsguilty', on_delete=models.CASCADE, null=True)
    text  = models.TextField(default='')
    location  = models.CharField(max_length=100, default='')
    server  = models.CharField(max_length=100, default='')
    resolved = models.BooleanField(default=False)


    @property
    def created_time(self):
        return int(self.created.timestamp())


    @property
    def caller_handle(self):
        if self.caller == None:
            return ""
        return self.caller.handle


    @property
    def guilty_handle(self):
        if self.guilty == None:
            return ""
        return self.guilty.handle


class PoliceReport(BaseModel):
    reporter = models.ForeignKey("account.Member", related_name='policereports', on_delete=models.CASCADE, null=True)
    crime = models.CharField(max_length=100, default='', choices=CRIME_CHOICES)
    guilty = models.ForeignKey("account.Member", related_name='reportguilties', on_delete=models.CASCADE, null=True)
    arrest = models.ForeignKey("police.Arrest", related_name='Reports', on_delete=models.CASCADE, null=True, default=None, blank=True)
    call = models.ForeignKey("police.PoliceCall", related_name='Reports', on_delete=models.CASCADE, null=True, default=None, blank=True)
    server  = models.CharField(max_length=100, default='')
    server_instance  = models.ForeignKey("game.Gameserver", on_delete=models.CASCADE, null=True)
    text  = models.TextField(default='')
    jail_time = models.IntegerField(default=60)
    ban_time = models.IntegerField(default=60)
    success = models.BooleanField(default=False)

    @property
    def created_time(self):
        return int(self.created.timestamp())
    
    
    @property
    def private_server(self):
        if self.server_instance:
            return self.server_instance.is_private
        
        return False

    @property
    def guilty_handle(self):
        if self.guilty == None:
            return ""
        return self.guilty.handle

    @property
    def reporter_handle(self):
        if self.reporter == None:
            return ""
        return self.reporter.handle


class Crime(BaseModel):
    member = models.ForeignKey("account.Member", related_name='crimes', on_delete=models.CASCADE, null=True)
    type = models.CharField(max_length=100, default='', choices=CRIME_CHOICES)
    is_jail = models.BooleanField(default=False)
    police_report = models.ForeignKey(PoliceReport, related_name='crimes', on_delete=models.CASCADE, null=True)
    jail_time = models.IntegerField(default=0)
    ban_time = models.IntegerField(default=0)

    @property
    def guilty_handle(self):
        if self.member == None:
            return ""
        return self.member.handle


class Arrest(BaseModel):
    police = models.ForeignKey("account.Member", related_name='arrests', on_delete=models.CASCADE, null=True)
    guilty = models.ForeignKey("account.Member", related_name='guiltyarrests', on_delete=models.CASCADE, null=True)
    report = models.ForeignKey(PoliceReport, related_name='arrests', on_delete=models.CASCADE, null=True, default=None, blank=True)

    @property
    def guilty_handle(self):
        if self.guilty == None:
            return ""
        return self.guilty.handle
    

    @property
    def police_handle(self):
        if self.police == None:
            return ""
        return self.police.handle


class JailExit(BaseModel):
    guilty = models.ForeignKey("account.Member", related_name='jailexits', on_delete=models.CASCADE, null=True)

    @property
    def guilty_handle(self):
        if self.guilty == None:
            return ""
        return self.guilty.handle


class Kill(BaseModel):
    killer = models.ForeignKey("account.Member", related_name='killers', on_delete=models.CASCADE, null=True)
    dead = models.ForeignKey("account.Member", related_name='deads', on_delete=models.CASCADE, null=True)

    @property
    def killer_handle(self):
        if self.killer == None:
            return ""
        return self.killer.handle

    @property
    def dead_handle(self):
        if self.dead == None:
            return ""
        return self.dead.handle



Q_TYPE_POLICE = "پلیس"

Q_TYPE_CHOICES = (
    (Q_TYPE_POLICE, Q_TYPE_POLICE),
)


class JobQueueMember(BaseModel):
    member = models.ForeignKey("account.Member", related_name='JobQs', on_delete=models.CASCADE, null=True)
    type = models.CharField(max_length=100, choices=Q_TYPE_CHOICES, default=Q_TYPE_POLICE, blank=True)
    cancelled = models.BooleanField(default=False)
    popped = models.BooleanField(default=False)
    score = models.IntegerField(default=0)


    @property
    def member_handle(self):
        if self.member == None:
            return ""
        return self.member.handle


    @property
    def title(self):
        if self.member == None:
            return ""
        return self.member.title


    @property
    def purchase_count(self):
        if self.member == None:
            return 0
        return Purchase.objects.filter(member=self.member, is_test=False).count()
    

    def cancel(self):
        self.cancelled = True
        self.score = 0
        self.save()
    

    def pop(self):
        if self.type == Q_TYPE_POLICE:
            self.member.police_job_enabled = True
            self.member.save()

        self.popped = True
        self.save()


class PoliceLaw(BaseModel):
    law = models.TextField(default="", blank=True)
    last_update = models.DateTimeField()
