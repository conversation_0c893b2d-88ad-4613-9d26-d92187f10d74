from rest_framework import serializers
from .models import PoliceCall, Crime, PoliceLaw, PoliceReport

class PoliceCallSerializer(serializers.ModelSerializer):
    class Meta:
        model = PoliceCall
        fields = ("id", "created_time", "caller_handle", "guilty_handle", "crime", "location", "text", "server")


class PoliceReportSerializer(serializers.ModelSerializer):
    class Meta:
        model = PoliceReport
        fields = ("id", "created_time", "guilty_handle", "crime", "success", "ban_time", "jail_time", "text")


class CrimeSerializer(serializers.ModelSerializer):
    class Meta:
        model = Crime
        fields = ("id", "created_time", "jail_time", "guilty_handle", "type", "is_jail", "ban_time")
