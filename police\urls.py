from django.urls import path
from .function_views import police_call, police_report, police_arrest, jail_exit, police_call_list
from .function_views import jail_stat, jail_reduce, arrest_list, i_killed
from .function_views import add_me_to_job_q, job_q_stats, add_score_to_job_q, police_law, can_damage


urlpatterns = [
    path(r'police_call/', police_call, name="police_call"),
    path(r'police_report/', police_report, name="police_report"),
    path(r'police_arrest/', police_arrest, name="police_arrest"),
    path(r'jail_exit/', jail_exit, name="jail_exit"),
    path(r'police_call_list/', police_call_list, name="police_call_list"),
    path(r'jail_stat/', jail_stat, name="jail_stat"),
    path(r'jail_reduce/', jail_reduce, name="jail_reduce"),
    path(r'arrest_list/', arrest_list, name="arrest_list"),
    path(r'i_kill/', i_killed, name="i_killed"),

    path(r'add_me_to_job_q/', add_me_to_job_q, name="add_me_to_job_q"),
    path(r'job_q_stats/', job_q_stats, name="job_q_stats"),
    path(r'add_score_to_job_q/', add_score_to_job_q, name="add_score_to_job_q"),
    path(r'police_law/', police_law, name="police_law"),

    path(r'can_damage/', can_damage, name="can_damage"),
]
