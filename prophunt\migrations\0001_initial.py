# Generated by Django 2.2.28 on 2025-07-30 13:46

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='BattlePassProgress',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('removed', models.DateTimeField(blank=True, default=None, editable=False, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('last_update', models.DateTimeField(auto_now=True)),
                ('current_tier', models.IntegerField(default=1, help_text='Current tier reached')),
            ],
        ),
        migrations.CreateModel(
            name='BattlePassReward',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('removed', models.DateTimeField(blank=True, default=None, editable=False, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('last_update', models.DateTimeField(auto_now=True)),
                ('tier_type', models.CharField(choices=[('free', 'Free Tier'), ('premium', 'Premium Tier')], max_length=20)),
                ('reward_type', models.CharField(choices=[('coin', 'Coins'), ('character', 'Character'), ('skin', 'Skin'), ('emote', 'Emote'), ('title', 'Title'), ('badge', 'Badge')], max_length=20)),
                ('name', models.CharField(help_text='Reward name', max_length=100)),
                ('description', models.TextField(blank=True, help_text='Reward description')),
                ('image_url', models.URLField(blank=True, help_text='Reward image URL')),
                ('coin_amount', models.IntegerField(default=0, help_text='Amount of coins (if reward_type is coin)')),
                ('item_id', models.CharField(blank=True, help_text='Item ID for characters, skins, etc.', max_length=100)),
            ],
        ),
        migrations.CreateModel(
            name='BattlePassTier',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('removed', models.DateTimeField(blank=True, default=None, editable=False, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('last_update', models.DateTimeField(auto_now=True)),
                ('tier_number', models.IntegerField(help_text='Tier number (1, 2, 3, etc.)')),
                ('smart_required', models.IntegerField(help_text='Total smart points required to reach this tier')),
            ],
            options={
                'ordering': ['season', 'tier_number'],
            },
        ),
        migrations.CreateModel(
            name='ClaimedReward',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('removed', models.DateTimeField(blank=True, default=None, editable=False, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('last_update', models.DateTimeField(auto_now=True)),
                ('claimed_at', models.DateTimeField(default=django.utils.timezone.now)),
            ],
        ),
        migrations.CreateModel(
            name='PropHuntGame',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('removed', models.DateTimeField(blank=True, default=None, editable=False, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('last_update', models.DateTimeField(auto_now=True)),
                ('server_id', models.CharField(help_text='Server identifier that hosts this game', max_length=100)),
                ('status', models.CharField(choices=[('waiting', 'Waiting for Players'), ('active', 'Game Active'), ('finished', 'Game Finished'), ('cancelled', 'Game Cancelled')], default='waiting', max_length=20)),
                ('start_time', models.DateTimeField(blank=True, help_text='When the game actually started', null=True)),
                ('end_time', models.DateTimeField(blank=True, help_text='When the game ended', null=True)),
                ('duration_seconds', models.IntegerField(default=0, help_text='Game duration in seconds')),
                ('max_players', models.IntegerField(default=8, help_text='Maximum number of players')),
                ('round_duration', models.IntegerField(default=300, help_text='Round duration in seconds')),
                ('winning_team', models.CharField(blank=True, choices=[('props', 'Props'), ('hunters', 'Hunters')], help_text='Which team won the game', max_length=20, null=True)),
            ],
            options={
                'ordering': ['-created'],
            },
        ),
        migrations.CreateModel(
            name='PropHuntSeason',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('removed', models.DateTimeField(blank=True, default=None, editable=False, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('last_update', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(help_text='Season name', max_length=100)),
                ('description', models.TextField(blank=True, help_text='Season description')),
                ('start_date', models.DateTimeField(help_text='Season start date')),
                ('end_date', models.DateTimeField(help_text='Season end date')),
                ('is_active', models.BooleanField(default=False, help_text='Whether this is the current active season')),
                ('max_tier', models.IntegerField(default=100, help_text='Maximum tier in battle pass')),
                ('smart_per_tier', models.IntegerField(default=100, help_text='Smart points required per tier')),
            ],
            options={
                'ordering': ['-start_date'],
            },
        ),
        migrations.CreateModel(
            name='PropHuntParticipant',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('removed', models.DateTimeField(blank=True, default=None, editable=False, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('last_update', models.DateTimeField(auto_now=True)),
                ('team', models.CharField(choices=[('props', 'Props'), ('hunters', 'Hunters')], max_length=20)),
                ('kills', models.IntegerField(default=0, help_text='Number of kills/finds')),
                ('deaths', models.IntegerField(default=0, help_text='Number of deaths/times found')),
                ('survived', models.BooleanField(default=False, help_text='Whether player survived the round (for props)')),
                ('coin_earned', models.IntegerField(default=0, help_text='Coins earned from this game')),
                ('smart_earned', models.IntegerField(default=0, help_text='Smart points earned from this game')),
                ('game', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='participants', to='prophunt.PropHuntGame')),
                ('member', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='prophunt_games', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.AddIndex(
            model_name='prophuntgame',
            index=models.Index(fields=['server_id', 'status'], name='prophunt_pr_server__484222_idx'),
        ),
        migrations.AddIndex(
            model_name='prophuntgame',
            index=models.Index(fields=['start_time'], name='prophunt_pr_start_t_20d2d4_idx'),
        ),
        migrations.AddField(
            model_name='claimedreward',
            name='member',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='claimed_rewards', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='claimedreward',
            name='reward',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='claimed_by', to='prophunt.BattlePassReward'),
        ),
        migrations.AddField(
            model_name='battlepasstier',
            name='season',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tiers', to='prophunt.PropHuntSeason'),
        ),
        migrations.AddField(
            model_name='battlepassreward',
            name='tier',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='rewards', to='prophunt.BattlePassTier'),
        ),
        migrations.AddField(
            model_name='battlepassprogress',
            name='member',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='battlepass_progress', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='battlepassprogress',
            name='season',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='player_progress', to='prophunt.PropHuntSeason'),
        ),
        migrations.AddIndex(
            model_name='prophuntparticipant',
            index=models.Index(fields=['member', 'team'], name='prophunt_pr_member__d8c269_idx'),
        ),
        migrations.AddIndex(
            model_name='prophuntparticipant',
            index=models.Index(fields=['game', 'team'], name='prophunt_pr_game_id_89bcd2_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='prophuntparticipant',
            unique_together={('game', 'member')},
        ),
        migrations.AddIndex(
            model_name='claimedreward',
            index=models.Index(fields=['member', 'reward'], name='prophunt_cl_member__65af3b_idx'),
        ),
        migrations.AddIndex(
            model_name='claimedreward',
            index=models.Index(fields=['claimed_at'], name='prophunt_cl_claimed_4e8c98_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='claimedreward',
            unique_together={('member', 'reward')},
        ),
        migrations.AlterUniqueTogether(
            name='battlepasstier',
            unique_together={('season', 'tier_number')},
        ),
        migrations.AddIndex(
            model_name='battlepassreward',
            index=models.Index(fields=['tier', 'tier_type'], name='prophunt_ba_tier_id_6b967e_idx'),
        ),
        migrations.AddIndex(
            model_name='battlepassreward',
            index=models.Index(fields=['reward_type'], name='prophunt_ba_reward__062cdf_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='battlepassreward',
            unique_together={('tier', 'tier_type')},
        ),
        migrations.AddIndex(
            model_name='battlepassprogress',
            index=models.Index(fields=['member', 'season'], name='prophunt_ba_member__675b95_idx'),
        ),
        migrations.AddIndex(
            model_name='battlepassprogress',
            index=models.Index(fields=['season', 'current_tier'], name='prophunt_ba_season__1d6a16_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='battlepassprogress',
            unique_together={('member', 'season')},
        ),
    ]
