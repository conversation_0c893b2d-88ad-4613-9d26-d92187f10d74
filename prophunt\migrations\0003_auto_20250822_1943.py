# Generated by Django 2.2.28 on 2025-08-22 16:13

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('prophunt', '0002_auto_20250730_1736'),
    ]

    operations = [
        migrations.CreateModel(
            name='ProphauntItemData',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('removed', models.DateTimeField(blank=True, default=None, editable=False, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('last_update', models.DateTimeField(auto_now=True)),
                ('grenades', models.IntegerField(default=3, help_text='Number of grenades available')),
                ('hexes', models.IntegerField(default=5, help_text='Number of hexes available')),
            ],
        ),
        migrations.CreateModel(
            name='ProphauntWeapon',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('removed', models.DateTimeField(blank=True, default=None, editable=False, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('last_update', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(help_text='Weapon name', max_length=100)),
                ('price', models.IntegerField(default=1000, help_text='Price in coins')),
                ('damage', models.IntegerField(default=100, help_text='Weapon damage value')),
                ('resource_path', models.CharField(help_text='Path to weapon resource/asset', max_length=200)),
                ('is_test', models.BooleanField(default=False, help_text='Whether this is a test weapon')),
                ('min_version', models.IntegerField(default=130, help_text='Minimum game version required')),
                ('order', models.IntegerField(default=1, help_text='Display order')),
            ],
            options={
                'ordering': ['order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='ProphauntWeaponOwns',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('removed', models.DateTimeField(blank=True, default=None, editable=False, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('last_update', models.DateTimeField(auto_now=True)),
                ('ammo', models.IntegerField(default=100, help_text='Current ammo count')),
                ('member', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='prophaunt_weapon_owns', to=settings.AUTH_USER_MODEL)),
                ('weapon', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='owned_by', to='prophunt.ProphauntWeapon')),
            ],
        ),
        migrations.AddIndex(
            model_name='prophauntweapon',
            index=models.Index(fields=['is_test', 'min_version'], name='prophunt_pr_is_test_7ce3e1_idx'),
        ),
        migrations.AddIndex(
            model_name='prophauntweapon',
            index=models.Index(fields=['order'], name='prophunt_pr_order_fae94a_idx'),
        ),
        migrations.AddField(
            model_name='prophauntitemdata',
            name='current_selected_weapon',
            field=models.ForeignKey(blank=True, help_text='Currently selected weapon', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='selected_by', to='prophunt.ProphauntWeapon'),
        ),
        migrations.AddField(
            model_name='prophauntitemdata',
            name='member',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='prophaunt_item_data', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='prophauntweaponowns',
            index=models.Index(fields=['member', 'weapon'], name='prophunt_pr_member__763511_idx'),
        ),
        migrations.AddIndex(
            model_name='prophauntweaponowns',
            index=models.Index(fields=['weapon'], name='prophunt_pr_weapon__8a533a_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='prophauntweaponowns',
            unique_together={('member', 'weapon')},
        ),
        migrations.AddIndex(
            model_name='prophauntitemdata',
            index=models.Index(fields=['member'], name='prophunt_pr_member__ed8909_idx'),
        ),
        migrations.AddIndex(
            model_name='prophauntitemdata',
            index=models.Index(fields=['current_selected_weapon'], name='prophunt_pr_current_add446_idx'),
        ),
    ]
