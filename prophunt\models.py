from django.db import models
from django.utils import timezone
from utils.models import BaseModel
from account.models import Member


class PropHuntGame(BaseModel):
    """Model to track PropHunt game sessions"""

    GAME_STATUS_CHOICES = [
        ('waiting', 'Waiting for Players'),
        ('active', 'Game Active'),
        ('finished', 'Game Finished'),
        ('cancelled', 'Game Cancelled'),
    ]

    server_id = models.CharField(max_length=100, help_text="Server identifier that hosts this game")
    status = models.CharField(max_length=20, choices=GAME_STATUS_CHOICES, default='waiting')
    start_time = models.DateTimeField(null=True, blank=True, help_text="When the game actually started")
    end_time = models.DateTimeField(null=True, blank=True, help_text="When the game ended")
    duration_seconds = models.IntegerField(default=0, help_text="Game duration in seconds")

    # Game settings
    max_players = models.IntegerField(default=8, help_text="Maximum number of players")
    round_duration = models.IntegerField(default=300, help_text="Round duration in seconds")

    # Results
    winning_team = models.Char<PERSON>ield(max_length=20, choices=[('props', 'Props'), ('hunters', 'Hunters')],
                                  null=True, blank=True, help_text="Which team won the game")
    props_round_wins = models.IntegerField(default=0, help_text="Number of rounds won by props team")
    hunters_round_wins = models.IntegerField(default=0, help_text="Number of rounds won by hunters team")

    class Meta:
        ordering = ['-created']
        indexes = [
            models.Index(fields=['server_id', 'status']),
            models.Index(fields=['start_time']),
        ]


    def __str__(self):
        return f"PropHunt Game {self.id} - {self.status} ({self.server_id})"


    def start_game(self):
        """Start the game"""
        self.status = 'active'
        self.start_time = timezone.now()
        self.save()


    def end_game(self, winning_team=None):
        """End the game and calculate results"""
        self.status = 'finished'
        self.end_time = timezone.now()
        self.winning_team = winning_team

        if self.start_time:
            self.duration_seconds = int((self.end_time - self.start_time).total_seconds())

        self.save()

        # Calculate rewards for all participants
        self._calculate_rewards()


    def _calculate_rewards(self):
        """Calculate and distribute rewards to players"""
        participants = self.participants.all()

        for participant in participants:
            base_coin = 10  # Base coin reward for participation
            base_smart = 5  # Base smart reward for participation

            # Bonus for winning team
            if participant.team == self.winning_team:
                base_coin += 15
                base_smart += 10

            # Bonus for performance
            if participant.kills > 0:
                base_coin += participant.kills * 5
                base_smart += participant.kills * 2

            # Survival bonus for props
            if participant.team == 'props' and participant.survived:
                base_coin += 10
                base_smart += 5

            # Apply rewards
            participant.coin_earned = base_coin
            participant.smart_earned = base_smart
            participant.save()

            # Add to member
            member = participant.member
            member.coin += base_coin
            member.smart += base_smart
            member.season_smart += base_smart
            member.save()


class PropHuntParticipant(BaseModel):
    """Model to track individual player participation in PropHunt games"""

    TEAM_CHOICES = [
        ('props', 'Props'),
        ('hunters', 'Hunters'),
    ]

    game = models.ForeignKey(PropHuntGame, related_name='participants', on_delete=models.CASCADE)
    member = models.ForeignKey(Member, related_name='prophunt_games', on_delete=models.CASCADE)
    server_id = models.CharField(max_length=100, help_text="Server identifier that hosts this game")
    team = models.CharField(max_length=20, choices=TEAM_CHOICES)

    # Game statistics
    kills = models.IntegerField(default=0, help_text="Number of kills/finds")
    deaths = models.IntegerField(default=0, help_text="Number of deaths/times found")
    survived = models.BooleanField(default=False, help_text="Whether player survived the round (for props)")

    # Rewards
    coin_earned = models.IntegerField(default=0, help_text="Coins earned from this game")
    smart_earned = models.IntegerField(default=0, help_text="Smart points earned from this game")

    class Meta:
        indexes = [
            models.Index(fields=['member', 'team']),
            models.Index(fields=['game', 'team']),
        ]

    def __str__(self):
        return f"{self.member.username} - {self.team} in Game {self.game.id}"


class PropHuntSeason(BaseModel):
    """Model to track PropHunt seasons for battle pass"""

    name = models.CharField(max_length=100, help_text="Season name")
    description = models.TextField(blank=True, help_text="Season description")
    start_date = models.DateTimeField(help_text="Season start date")
    end_date = models.DateTimeField(help_text="Season end date")
    is_active = models.BooleanField(default=False, help_text="Whether this is the current active season")

    # Battle pass settings
    max_tier = models.IntegerField(default=100, help_text="Maximum tier in battle pass")
    smart_per_tier = models.IntegerField(default=100, help_text="Smart points required per tier")

    class Meta:
        ordering = ['-start_date']

    def __str__(self):
        return f"Season {self.name}"

    @classmethod
    def get_current_season(cls):
        """Get the current active season"""
        return cls.objects.filter(is_active=True).first()

    def activate(self):
        """Activate this season and deactivate others"""
        # Deactivate all other seasons
        PropHuntSeason.objects.update(is_active=False)

        # Activate this season
        self.is_active = True
        self.save()

        # Reset all members' season smart
        Member.objects.update(season_smart=0, season_premium=False)


class BattlePassTier(BaseModel):
    """Model to define battle pass tiers and their requirements"""

    season = models.ForeignKey(PropHuntSeason, related_name='tiers', on_delete=models.CASCADE)
    tier_number = models.IntegerField(help_text="Tier number (1, 2, 3, etc.)")
    smart_required = models.IntegerField(help_text="Total smart points required to reach this tier")

    class Meta:
        unique_together = ['season', 'tier_number']
        ordering = ['season', 'tier_number']

    def __str__(self):
        return f"Tier {self.tier_number} - {self.season.name}"


class BattlePassReward(BaseModel):
    """Model to define rewards for battle pass tiers"""

    REWARD_TYPE_CHOICES = [
        ('coin', 'Coins'),
        ('character', 'Character'),
        ('skin', 'Skin'),
        ('emote', 'Emote'),
        ('title', 'Title'),
        ('badge', 'Badge'),
    ]

    TIER_TYPE_CHOICES = [
        ('free', 'Free Tier'),
        ('premium', 'Premium Tier'),
    ]

    tier = models.ForeignKey(BattlePassTier, related_name='rewards', on_delete=models.CASCADE)
    tier_type = models.CharField(max_length=20, choices=TIER_TYPE_CHOICES)
    reward_type = models.CharField(max_length=20, choices=REWARD_TYPE_CHOICES)

    # Reward details
    name = models.CharField(max_length=100, help_text="Reward name")
    description = models.TextField(blank=True, help_text="Reward description")
    image_url = models.URLField(blank=True, help_text="Reward image URL")

    # Reward values
    coin_amount = models.IntegerField(default=0, help_text="Amount of coins (if reward_type is coin)")
    item_id = models.CharField(max_length=100, blank=True, help_text="Item ID for characters, skins, etc.")

    class Meta:
        unique_together = ['tier', 'tier_type']
        indexes = [
            models.Index(fields=['tier', 'tier_type']),
            models.Index(fields=['reward_type']),
        ]

    def __str__(self):
        return f"{self.name} - {self.tier_type} (Tier {self.tier.tier_number})"


class BattlePassProgress(BaseModel):
    """Model to track individual player progress in battle pass"""

    member = models.ForeignKey(Member, related_name='battlepass_progress', on_delete=models.CASCADE)
    season = models.ForeignKey(PropHuntSeason, related_name='player_progress', on_delete=models.CASCADE)
    current_tier = models.IntegerField(default=0, help_text="Current tier reached")

    class Meta:
        unique_together = ['member', 'season']
        indexes = [
            models.Index(fields=['member', 'season']),
            models.Index(fields=['season', 'current_tier']),
        ]

    def __str__(self):
        return f"{self.member.username} - {self.season.name} (Tier {self.current_tier})"

    def update_tier(self):
        """Update player's tier based on their season smart"""
        if not self.season.smart_per_tier:
            return

        # Calculate tier based on available tiers and their smart requirements
        available_tiers = self.season.tiers.filter(
            smart_required__lte=self.member.season_smart
        ).order_by('-tier_number')

        if available_tiers.exists():
            new_tier = available_tiers.first().tier_number
        else:
            new_tier = 0  # No tier reached yet

        if new_tier > self.current_tier:
            self.current_tier = new_tier
            self.save()

        return self.current_tier


class ClaimedReward(BaseModel):
    """Model to track which rewards have been claimed by players"""

    member = models.ForeignKey(Member, related_name='claimed_rewards', on_delete=models.CASCADE)
    reward = models.ForeignKey(BattlePassReward, related_name='claimed_by', on_delete=models.CASCADE)
    claimed_at = models.DateTimeField(default=timezone.now)

    class Meta:
        unique_together = ['member', 'reward']
        indexes = [
            models.Index(fields=['member', 'reward']),
            models.Index(fields=['claimed_at']),
        ]

    def __str__(self):
        return f"{self.member.username} claimed {self.reward.name}"


class ProphauntWeapon(BaseModel):
    """Model to define PropHunt weapons available for purchase"""

    name = models.CharField(max_length=100, help_text="Weapon name")
    price = models.IntegerField(default=1000, help_text="Price in coins")
    damage = models.IntegerField(default=100, help_text="Weapon damage value")
    resource_path = models.CharField(max_length=200, help_text="Path to weapon resource/asset")

    # Optional fields for weapon properties
    is_test = models.BooleanField(default=False, help_text="Whether this is a test weapon")
    min_version = models.IntegerField(default=130, help_text="Minimum game version required")
    order = models.IntegerField(default=1, help_text="Display order")

    class Meta:
        ordering = ['order', 'name']
        indexes = [
            models.Index(fields=['is_test', 'min_version']),
            models.Index(fields=['order']),
        ]

    def __str__(self):
        return self.name


class ProphauntWeaponOwns(BaseModel):
    """Model to track weapon ownership by players"""

    member = models.ForeignKey(Member, related_name='prophaunt_weapon_owns', on_delete=models.CASCADE)
    weapon = models.ForeignKey(ProphauntWeapon, related_name='owned_by', on_delete=models.CASCADE)

    # Optional fields for weapon state
    ammo = models.IntegerField(default=100, help_text="Current ammo count")

    class Meta:
        unique_together = ['member', 'weapon']
        indexes = [
            models.Index(fields=['member', 'weapon']),
            models.Index(fields=['weapon']),
        ]

    def __str__(self):
        return f"{self.member.username} owns {self.weapon.name}"


class ProphauntItemData(BaseModel):
    """Model to hold PropHunt item data for each member"""

    member = models.OneToOneField(Member, related_name='prophaunt_item_data', on_delete=models.CASCADE)
    current_selected_weapon = models.ForeignKey(
        ProphauntWeapon,
        related_name='selected_by',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="Currently selected weapon"
    )
    grenades = models.IntegerField(default=3, help_text="Number of grenades available")
    hexes = models.IntegerField(default=5, help_text="Number of hexes available")

    class Meta:
        indexes = [
            models.Index(fields=['member']),
            models.Index(fields=['current_selected_weapon']),
        ]

    def __str__(self):
        return f"{self.member.username} PropHunt data"
