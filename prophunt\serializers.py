from rest_framework import serializers
from account.models import Member
from .models import (
    PropHuntGame, PropHuntParticipant, PropHuntSeason,
    BattlePassTier, BattlePassReward, BattlePassProgress, ClaimedReward,
    ProphauntWeapon, ProphauntWeaponOwns, ProphauntItemData
)


class MemberBasicSerializer(serializers.ModelSerializer):
    """Basic member serializer for PropHunt responses"""
    
    class Meta:
        model = Member
        fields = ['id', 'username', 'handle', 'coin', 'smart', 'season_smart', 'season_premium']


class PropHuntParticipantSerializer(serializers.ModelSerializer):
    """Serializer for PropHunt game participants"""

    member = MemberBasicSerializer(read_only=True)
    member_id = serializers.IntegerField(write_only=True)

    class Meta:
        model = PropHuntParticipant
        fields = [
            'id', 'member', 'member_id', 'server_id', 'team', 'kills', 'deaths',
            'survived', 'coin_earned', 'smart_earned', 'created'
        ]


class PropHuntGameSerializer(serializers.ModelSerializer):
    """Serializer for PropHunt games"""
    
    participants = PropHuntParticipantSerializer(many=True, read_only=True)
    participant_count = serializers.SerializerMethodField()
    props_count = serializers.SerializerMethodField()
    hunters_count = serializers.SerializerMethodField()
    
    class Meta:
        model = PropHuntGame
        fields = [
            'id', 'server_id', 'status', 'start_time', 'end_time',
            'duration_seconds', 'max_players', 'round_duration',
            'winning_team', 'props_round_wins', 'hunters_round_wins',
            'participants', 'participant_count', 'props_count',
            'hunters_count', 'created', 'last_update'
        ]
    
    def get_participant_count(self, obj):
        return obj.participants.count()
    
    def get_props_count(self, obj):
        return obj.participants.filter(team='props').count()
    
    def get_hunters_count(self, obj):
        return obj.participants.filter(team='hunters').count()


class PropHuntSeasonSerializer(serializers.ModelSerializer):
    """Serializer for PropHunt seasons"""
    
    total_tiers = serializers.SerializerMethodField()
    active_players = serializers.SerializerMethodField()
    
    class Meta:
        model = PropHuntSeason
        fields = [
            'id', 'name', 'description', 'start_date', 'end_date', 
            'is_active', 'max_tier', 'smart_per_tier', 'total_tiers',
            'active_players', 'created', 'last_update'
        ]
    
    def get_total_tiers(self, obj):
        return obj.tiers.count()
    
    def get_active_players(self, obj):
        return obj.player_progress.count()


class BattlePassTierSerializer(serializers.ModelSerializer):
    """Serializer for battle pass tiers"""
    
    season_name = serializers.CharField(source='season.name', read_only=True)
    reward_count = serializers.SerializerMethodField()
    
    class Meta:
        model = BattlePassTier
        fields = [
            'id', 'season', 'season_name', 'tier_number', 
            'smart_required', 'reward_count', 'created', 'last_update'
        ]
    
    def get_reward_count(self, obj):
        return obj.rewards.count()


class BattlePassRewardSerializer(serializers.ModelSerializer):
    """Serializer for battle pass rewards"""
    
    tier_number = serializers.IntegerField(source='tier.tier_number', read_only=True)
    season_name = serializers.CharField(source='tier.season.name', read_only=True)
    
    class Meta:
        model = BattlePassReward
        fields = [
            'id', 'tier', 'tier_number', 'season_name', 'tier_type', 
            'reward_type', 'name', 'description', 'image_url', 
            'coin_amount', 'item_id', 'created', 'last_update'
        ]


class BattlePassProgressSerializer(serializers.ModelSerializer):
    """Serializer for battle pass progress"""
    
    member = MemberBasicSerializer(read_only=True)
    season_name = serializers.CharField(source='season.name', read_only=True)
    progress_percentage = serializers.SerializerMethodField()
    next_tier_smart_required = serializers.SerializerMethodField()
    smart_to_next_tier = serializers.SerializerMethodField()
    
    class Meta:
        model = BattlePassProgress
        fields = [
            'id', 'member', 'season', 'season_name', 'current_tier',
            'progress_percentage', 'next_tier_smart_required', 
            'smart_to_next_tier', 'created', 'last_update'
        ]
    
    def get_progress_percentage(self, obj):
        if obj.season.max_tier == 0:
            return 0
        return min((obj.current_tier / obj.season.max_tier) * 100, 100)
    
    def get_next_tier_smart_required(self, obj):
        if obj.current_tier >= obj.season.max_tier:
            return None
        return (obj.current_tier) * obj.season.smart_per_tier
    
    def get_smart_to_next_tier(self, obj):
        if obj.current_tier >= obj.season.max_tier:
            return 0
        next_tier_required = (obj.current_tier) * obj.season.smart_per_tier
        return max(0, next_tier_required - obj.member.season_smart)


class ClaimedRewardSerializer(serializers.ModelSerializer):
    """Serializer for claimed rewards"""
    
    member = MemberBasicSerializer(read_only=True)
    reward = BattlePassRewardSerializer(read_only=True)
    
    class Meta:
        model = ClaimedReward
        fields = [
            'id', 'member', 'reward', 'claimed_at', 'created', 'last_update'
        ]


class PropHuntGameStatsSerializer(serializers.Serializer):
    """Serializer for PropHunt game statistics"""
    
    total_games = serializers.IntegerField()
    total_wins = serializers.IntegerField()
    total_kills = serializers.IntegerField()
    total_deaths = serializers.IntegerField()
    win_rate = serializers.FloatField()
    kd_ratio = serializers.FloatField()
    favorite_team = serializers.CharField()
    total_coins_earned = serializers.IntegerField()
    total_smart_earned = serializers.IntegerField()


class BattlePassSummarySerializer(serializers.Serializer):
    """Serializer for battle pass summary"""
    
    season = PropHuntSeasonSerializer()
    progress = BattlePassProgressSerializer()
    available_rewards = serializers.IntegerField()
    claimed_rewards = serializers.IntegerField()
    unclaimed_rewards = serializers.IntegerField()
    premium_rewards_available = serializers.IntegerField()
    next_milestone_tier = serializers.IntegerField()
    next_milestone_smart_required = serializers.IntegerField()


class PropHuntLeaderboardSerializer(serializers.Serializer):
    """Serializer for PropHunt leaderboard"""
    
    member = MemberBasicSerializer()
    rank = serializers.IntegerField()
    season_smart = serializers.IntegerField()
    total_games = serializers.IntegerField()
    win_rate = serializers.FloatField()
    current_tier = serializers.IntegerField()


# Request serializers for API endpoints
class StartGameRequestSerializer(serializers.Serializer):
    """Serializer for start game request"""
    
    server_id = serializers.CharField(max_length=100)
    max_players = serializers.IntegerField(default=8, min_value=2, max_value=16)
    round_duration = serializers.IntegerField(default=300, min_value=60, max_value=1800)
    players = serializers.ListField(
        child=serializers.DictField(child=serializers.CharField()),
        min_length=2
    )


class EndGameRequestSerializer(serializers.Serializer):
    """Serializer for end game request"""
    
    game_id = serializers.IntegerField()
    winning_team = serializers.ChoiceField(choices=['props', 'hunters'], required=False)
    player_stats = serializers.ListField(
        child=serializers.DictField(child=serializers.IntegerField()),
        required=False
    )


class ClaimRewardRequestSerializer(serializers.Serializer):
    """Serializer for claim reward request"""
    
    reward_id = serializers.IntegerField()


# Response serializers
class GameStartResponseSerializer(serializers.Serializer):
    """Serializer for game start response"""
    
    message = serializers.CharField()
    game_id = serializers.IntegerField()
    game = PropHuntGameSerializer()


class GameEndResponseSerializer(serializers.Serializer):
    """Serializer for game end response"""
    
    message = serializers.CharField()
    game = PropHuntGameSerializer()
    participants = PropHuntParticipantSerializer(many=True)


class ClaimRewardResponseSerializer(serializers.Serializer):
    """Serializer for claim reward response"""

    message = serializers.CharField()
    reward = BattlePassRewardSerializer()


class ProphauntWeaponSerializer(serializers.ModelSerializer):
    """Serializer for PropHunt weapons"""

    class Meta:
        model = ProphauntWeapon
        fields = [
            'id', 'name', 'price', 'damage', 'resource_path',
            'is_test', 'min_version', 'order'
        ]


class ProphauntWeaponOwnsSerializer(serializers.ModelSerializer):
    """Serializer for PropHunt weapon ownership"""

    weapon = ProphauntWeaponSerializer(read_only=True)

    class Meta:
        model = ProphauntWeaponOwns
        fields = ['id', 'weapon', 'ammo']


class ProphauntItemDataSerializer(serializers.ModelSerializer):
    """Serializer for PropHunt item data"""

    current_selected_weapon = ProphauntWeaponSerializer(read_only=True)

    class Meta:
        model = ProphauntItemData
        fields = [
            'id', 'current_selected_weapon', 'grenades', 'hexes'
        ]
