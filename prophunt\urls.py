from django.urls import path
from . import views

urlpatterns = [
    # PropHunt game management
    path('end_game/', views.end_prophunt_game, name='end_prophunt_game'),

    # Battle pass APIs
    path('battlepass/progress/', views.get_battlepass_progress, name='get_battlepass_progress'),
    path('battlepass/claim_reward/', views.claim_battlepass_reward, name='claim_battlepass_reward'),

    # Weapon APIs
    path('weapons/list/', views.list_prophaunt_weapons, name='list_prophaunt_weapons'),
    path('weapons/purchase/', views.purchase_prophaunt_weapon, name='purchase_prophaunt_weapon'),
    path('weapons/select/', views.update_selected_weapon, name='update_selected_weapon'),

    # Item data APIs
    path('item_data/client/', views.get_prophaunt_item_data_client, name='get_prophaunt_item_data_client'),
    path('item_data/server/', views.get_prophaunt_item_data_server, name='get_prophaunt_item_data_server'),
    path('item_data/server/update/', views.update_prophaunt_item_data_server, name='update_prophaunt_item_data_server'),

    # Item purchase API
    path('items/purchase/', views.purchase_prophaunt_item, name='purchase_prophaunt_item'),
]
