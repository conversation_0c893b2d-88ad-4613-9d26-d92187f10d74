from account.models.member import Member

GOOROOHBAN = "گروهبان"
OSTOVAR = "استوار"
SARVAN = "سروان"
SARGORD = "سرگرد"
SARHANG = "سرهنگ"
SARDAR = "سردار"
SARLASHKAR = "امیر سرلشکر"
BAZRAS = "بازرس کل"
PRESIDENT = "رییس جمهور"
POLICE_TITLES = ['پلیس', GOOROOHBAN, OSTOVAR, SARVAN, SARGORD, SARHANG, SARDAR, SARLASHKAR, BAZRAS, PRESIDENT]
ADD_COINS = [       0,        0,        0,     1000,     0,      2000,   3000,      0,         0,       0 ]

officers = Member.objects.filter(title__in=POLICE_TITLES)
for officer in officers:
    rank = POLICE_TITLES.index(officer.title)
    coin = ADD_COINS[rank]
    print(officer.id, " ", officer.username, " ", officer.handle, " ", officer.title, " should get: ", coin)
    officer.title = GOOROOHBAN
    officer.coin += coin
    officer.save()
