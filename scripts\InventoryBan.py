import json
from account.models.member import Member, MemberData
from utils.utils import check_inventory_ban


print("starting all queries")
members = Member.objects.filter(id__gt=1000).filter(full_ban=True)
print("Done. Checking inv")
f = open("newinv.txt", "w")
count = 0
ban_count = 0
for member in members:
    count += 1
    if check_inventory_ban(member):
        print(member.id, " ", member.username, " ", member.data.first().data["inventory"]["inventory"])
        #f.write(str(count) + ": " + str(member.id) + " " + str(member.username) + " " + str(member.data.first().data["inventory"]["inventory"]) + "\n\n")
        ban_count += 1
        
        
        #Remove ban and fix inventory
        # data = member.data.first().data
        # data["inventory"] = {}
        # meber_data = member.data.first()
        # meber_data.data_string = json.dumps(data)
        # meber_data.save()
        # member.full_ban = False
        # member.save()

    if count % 5000 == 0:
        print(f"Checked {count} members so far.")


f.close()
print("Total Bans = ", ban_count)
