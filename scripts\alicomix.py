import random
from account.models.member import Member, ContentCreator
from django.utils import timezone
from datetime import timed<PERSON>ta
from utils.utils import send_email

alicomix = ContentCreator.objects.get(id=47)

rands = random.choices(alicomix.referers.all(), k=50)

email_body = ""
index = 1
for member in rands:
    member.coin += 10000
    member.save()
    print(index, ": ", member.handle, " ", member.username, " ", member.coin)
    email_body += str(index) + ": " + member.handle + " " + member.username + "\n"
    index += 1

#print(email_body)
send_email(["<EMAIL>"], "AliComix", email_body)
