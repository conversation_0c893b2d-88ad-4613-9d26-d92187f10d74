#usage python3 uploader.py /home/<USER>/salam.txt asghar.txt

import sys
import boto3
import logging 
from botocore.exceptions import ClientError
from django.conf import settings

logging.basicConfig(level=logging.INFO)

url = "https://s3.ir-thr-at1.arvanstorage.ir"
access_key = "df576247-dfdc-4268-a756-5bd187cee00f"
secret_key = "24a668edb12b3844a3ae9510a831649a3c1d0fb690160d91a30fb0f382ff8e31"

try:
   s3_resource = boto3.resource(
       's3',
       endpoint_url=url,
       aws_access_key_id=access_key,
       aws_secret_access_key=secret_key
   )
except Exception as exc:
   logging.info(exc)
else:
   try:
        bucket = s3_resource.Bucket('animalrush')

        for obj in bucket.objects.all():
           print(obj.key)
        
        response = bucket.delete_objects(
           Delete={
               'Objects': [
                   {
                       'Key': '*',
                   },
               ],
               'Quiet': True
           },
           MFA='string',
           RequestPayer='requester',
           BypassGovernanceRetention=True,
           ExpectedBucketOwner='string'
       )
        
        print(response)
   except ClientError as e:
       logging.error(e)
