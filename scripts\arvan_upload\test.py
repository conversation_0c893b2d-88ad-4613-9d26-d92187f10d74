import boto3
import logging 
from botocore.exceptions import ClientError
from django.conf import settings

logging.basicConfig(level=logging.INFO)

url = "https://s3.ir-thr-at1.arvanstorage.ir"
access_key = "df576247-dfdc-4268-a756-5bd187cee00f"
secret_key = "24a668edb12b3844a3ae9510a831649a3c1d0fb690160d91a30fb0f382ff8e31"

file_path = "/home/<USER>/develop/game_backend/scripts/arvan_upload/uploader.py"

try:
   s3_resource = boto3.resource(
       's3',
       endpoint_url=url,
       aws_access_key_id=access_key,
       aws_secret_access_key=secret_key
   )
except Exception as exc:
   logging.info(exc)
else:
   try:
       bucket = s3_resource.Bucket('animalrush')
       file_path = file_path
       object_name = 'salam.salam'

       with open(file_path, "rb") as file:
           bucket.put_object(
               ACL='public',
               Body=file,
               Key=object_name
           )
   except ClientError as e:
       logging.error(e)