    #f = open("/mnt/c/Users/<USER>/Desktop/bans.txt")
f = open("/home/<USER>/backend/inventorybans.txt")

lines = f.readlines()
usernames = []
for line in lines:
    if len(line) < 2:
        continue
    username = line.split(" ")[2]
    print(username)
    usernames.append(line.split(" ")[2])


print(len(usernames))

from account.models.member import *
users = Member.objects.filter(username__in=usernames)

for m in users:
    m.full_ban = False
    data = m.data.first().data
    data["inventory"] = {}
    meber_data = m.data.first()
    meber_data.data_string = json.dumps(data)
    meber_data.save()
    m.save()
