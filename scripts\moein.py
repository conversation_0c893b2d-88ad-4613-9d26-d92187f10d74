from account.models.member import Member
from django.utils import timezone
from datetime import timedelta
from utils.utils import send_email

from shop.models import *

start_time = timezone.now() - timedelta(days=6)

ps = Purchase.objects.filter(item__id=24, created__gt=start_time, is_test=False)

members = dict()
total_count = 0
total_price = 0

for p in ps:
    total_count += 1
    total_price += p.paid_price
    if members.get(p.member.id, None) == None:
        members[p.member.id] = 1
    else:
        members[p.member.id] += 1

sorted_members = dict(sorted(members.items(), key=lambda item: item[1], reverse=True))
email_body = ""
rank = 1

for key in sorted_members:
    member = Member.objects.get(id=key)
    email_body += str(rank) + ": " + member.username + " (" + member.handle + ") " + " : " + str(members[key]) + "\n"
    #email_body += str(rank) + ": (" + member.handle + ") " + " : " + str(members[key]) + "\n"
    rank += 1

email_body += "\n\n"
email_body += "total count: " + str(total_count) + " total price: " + str(total_price)
print(email_body)

send_email(["<EMAIL>", "<EMAIL>"], "قرعه کشی معین", email_body)
