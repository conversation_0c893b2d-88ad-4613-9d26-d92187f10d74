from account.models.member import Member, MemberD<PERSON>, ContentCreator
from game.models import JobDone, CoinTransfer
from django.utils import timezone
from datetime import timedelta
from utils.utils import send_email

BAN_TIME = 3600 * 24 * 7
JOB_DONE_THRESHOLD = 100
TIME_DELTA = timedelta(hours=48)
COIN_PENALTY = 200

# member.add_ban(seconds, False)

polices = Member.objects.filter(police_job_enabled=True)

message = ""

ban_count = 0
member:Member
for member in polices:
    if member.is_ban:
        continue
    
    count = JobDone.objects.filter(member=member, job="policeReport").filter(created__gt=timezone.now() - TIME_DELTA).count()
    if count > JOB_DONE_THRESHOLD:
        ban_count += 1
        penalty = count * COIN_PENALTY
        message += str(ban_count) + ": -" + str(penalty) + " :" + member.username + " " + member.handle + "\n"


send_email.delay("sinazand<PERSON><PERSON>@gmail.com", "Police Ban Job", message)
