SELECT count(*) FROM pg_stat_activity WHERE state <> 'idle' AND query NOT LIKE '% FROM pg_stat_activity %';


#Terminate all

select pg_terminate_backend(pid)
from (
  SELECT
      pid
  FROM pg_stat_activity
     WHERE query <> '<insufficient privilege>'
     AND state <> 'idle'
     AND pid <> pg_backend_pid()
    --  AND query_start < now() - interval '1 minute'
     ORDER BY query_start DESC) pid;