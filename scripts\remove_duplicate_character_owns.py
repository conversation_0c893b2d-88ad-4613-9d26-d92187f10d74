from account.models.member import *
from shop.models import *
from django.db.models import Count

mems = Member.objects.all()

ct = 1
for m in mems:
    duplicates = CharacterOwn.objects.filter(member=m).annotate(char_count=Count('character')).filter(char_count__gt=1)
    if len(duplicates) > 0:
        print(duplicates)
    ct += 1
    if ct % 1000 == 0:
        print(ct)

    print("**********************")
