import requests
from django.contrib import admin
from django.contrib.admin import <PERSON><PERSON>istFilter
from utils.admin import BaseAdmin
from .models import Character, CharacterOwn, ShopItem, Purchase, ChestOpen, AnimationPurchase, MARKET_ZARINPAL, Gift
from .function_views import Z<PERSON><PERSON><PERSON>L_MERCHANT_ID
from django.db.models import Q

class CharacterAdmin(BaseAdmin):
    list_display = ('id', 'name', 'tier', 'unlock_with_coin', 'is_test')
    list_editable = ('tier', 'unlock_with_coin', 'is_test')
    list_filter = ('is_test', 'created', 'tier')


class CharacterOwnAdmin(BaseAdmin):
    list_display = ('id', 'character', 'member', 'handle', 'created', 'removed')
    list_editable = ()
    raw_id_fields = ('member',)
    search_fields = ('member__id', 'member__handle', 'member__username')
    list_filter = ('created', 'removed')


class ShopItemAdmin(BaseAdmin):
    list_display = ('id', 'title', 'coins', 'price_str', 'price_tag', 'sku', 'order', 'is_active', 'in_site_shop', 'is_test')
    list_editable = ('order', 'is_test', 'is_active', 'in_site_shop')
    list_filter = ('is_active', 'is_test')
    filter_horizontal = ('unlock_characters',)


def make_test(modeladmin, request, queryset):
    queryset.update(is_test=True)
make_test.short_description = "Make Selected Purchases is_test=True"


def make_not_test(modeladmin, request, queryset):
    queryset.update(is_test=False)
make_not_test.short_description = "Make Selected Purchases is_test=False"


def apply_purchase(modeladmin, request, queryset):
    for purchase in queryset:
        purchase.apply()
apply_purchase.short_description = "Apply Purchases"


def verify_zarinpal(modeladmin, request, queryset):
    purchase: Purchase
    for purchase in queryset:
        if purchase.market != MARKET_ZARINPAL:
            continue

        purchase.ongoing = False
        purchase.is_verified = True

        response = requests.post("https://payment.zarinpal.com/pg/v4/payment/verify.json", json={
            "merchant_id": ZARINPAL_MERCHANT_ID,
            "amount": purchase.paid_price * 10,
            "authority": purchase.zarinpal_authority,
        })
        if response.status_code == 200:
            purchase.zarinpal_response = response.json()
            if response.json()["data"].get("code", 0) == 100:
                #prize = purchase.apply(is_manual=False)
                purchase.zarinpal_code  = response.json()["data"].get("code", 0)
                purchase.is_test = False
            elif response.json()["data"].get("code", 0) == 101:
                purchase.zarinpal_code  = response.json()["data"].get("code", 0)
                purchase.is_test = False
            else:
                purchase.is_test = True
        else:
            purchase.zarinpal_response = response.json()
            purchase.zarinpal_code = response.status_code
            purchase.is_test = True
            
            
        purchase.save()
verify_zarinpal.short_description = "Verify zarinpal"


def verify_and_apply_zarinpal(modeladmin, request, queryset):
    purchase: Purchase
    for purchase in queryset:
        if purchase.market != MARKET_ZARINPAL:
            continue

        purchase.ongoing = False
        purchase.is_verified = True


        response = requests.post("https://payment.zarinpal.com/pg/v4/payment/verify.json", json={
            "merchant_id": ZARINPAL_MERCHANT_ID,
            "amount": purchase.paid_price * 10,
            "authority": purchase.zarinpal_authority,
        })
        if response.status_code == 200:
            purchase.zarinpal_response = response.json()
            if response.json()["data"].get("code", 0) == 100:
                #prize = purchase.apply(is_manual=False)
                purchase.zarinpal_code  = response.json()["data"].get("code", 0)
                purchase.is_test = False
                purchase.apply()
                
            elif response.json()["data"].get("code", 0) == 101:
                purchase.zarinpal_code  = response.json()["data"].get("code", 0)
                purchase.is_test = False
                purchase.apply()
            else:
                purchase.is_test = True
        else:
            purchase.zarinpal_response = response.json()
            purchase.zarinpal_code = response.status_code
            purchase.is_test = True
            
            
        purchase.save()
verify_and_apply_zarinpal.short_description = "Verify and Apply zarinpal"

class ZarinpalFilter(SimpleListFilter):
    title = 'Success'
    parameter_name = "Success"
    def lookups(self, request, model_admin):
        return [("market", "market"), ("zarinpal", "zarinpal")]

    def queryset(self, request, queryset):
        if self.value() is None: 
            return queryset.all()
        if self.value() == 'market':
            return queryset.filter(Q(is_test=False) | (Q(market=MARKET_ZARINPAL) & (Q(zarinpal_code=100) | Q(zarinpal_code=101)) ))
        if self.value() == "zarinpal":
            return queryset.filter((Q(market=MARKET_ZARINPAL) & (Q(zarinpal_code=100) | Q(zarinpal_code=101)) ))



class PurchaseAdmin(BaseAdmin):
    list_display = ('id', 'created', 'member', 'handle', 'item', 'market', 'price', 'is_verified', 'is_manual', 'is_test', 'paid_price', 'zarinpal_code', 'is_gift', 'from_site', 'referer_content_creator', 'token', 'phone_number', 'is_restore')
    list_editable = ('is_test',)
    search_fields = ('member__handle', 'member__id', 'member__username', 'token')
    list_filter = ('created', 'is_test', 'is_verified', 'market', 'is_manual', 'is_restore', 'from_site', 'is_gift', 'zarinpal_code', 'item')
    raw_id_fields = ('member',)
    actions = [verify_and_apply_zarinpal, verify_zarinpal, apply_purchase]

    def get_search_results(self, request, queryset, search_term):
        new_search_term = search_term
        if isinstance(search_term, str):
            new_search_term = search_term.replace('۰', '0').replace('۱', '1').replace('۲', '2').replace('۳', '3').replace('۴', '4')
            new_search_term = new_search_term.replace('۵', '5').replace('۶', '6').replace('۷', '7').replace('۸', '8').replace('۹', '9')
        return super().get_search_results(request, queryset, new_search_term)


class ChestOpenAdmin(BaseAdmin):
    list_display = ('id', 'created', 'is_exact', 'tier', 'price', 'member', 'handle', 'unlocked_character', 'success', 'low_coin', 'unlocked_all')
    list_editable = ()
    search_fields = ('member__handle', 'member__id')
    list_filter = ('tier', 'success', 'low_coin', 'unlocked_all')


class AnimationPurchaseAdmin(BaseAdmin):
    list_display = ('id', 'created', 'member', 'handle', 'character', 'animation_str', 'animation_id')
    list_editable = ()
    search_fields = ('member__handle', 'member__id')
    list_filter = ('character',)


class GiftAdmin(BaseAdmin):
    list_display = ('id', 'created', 'member', 'price', 'coins', 'text', 'have_seen')
    list_editable = ()
    raw_id_fields = ('member', 'purchase')
    search_fields = ('member__handle', 'member__id', 'member__username')
    list_filter = ('have_seen', 'created')


admin.site.register(Gift, GiftAdmin)
admin.site.register(Purchase, PurchaseAdmin)
admin.site.register(ShopItem, ShopItemAdmin)
admin.site.register(Character, CharacterAdmin)
admin.site.register(CharacterOwn, CharacterOwnAdmin)
admin.site.register(ChestOpen, ChestOpenAdmin)
admin.site.register(AnimationPurchase, AnimationPurchaseAdmin)
