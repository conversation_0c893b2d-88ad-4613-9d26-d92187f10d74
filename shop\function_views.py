import random, logging, requests
from datetime import <PERSON><PERSON><PERSON>
from rest_framework import permissions
from rest_framework.decorators import api_view, permission_classes, renderer_classes
from rest_framework_jwt.settings import api_settings
from rest_framework_swagger import renderers
from django.http.response import JsonResponse
from django.utils import timezone
from django.shortcuts import render
from django.http import HttpResponse
from django.template import loader

from account.models.member import Member, MARKET_GOOGLEPLAY, MARKET_ZARINPAL, MARKET_DESKTOP, MARKET_BAZAAR, MARKET_MYKET
from account.models.sync_data import SyncData
from game.models import GameSettings, PurchaseWithCoin, AddCoin
from police.models import JobQueueMember
from .serializers import CharacterSerializer, ShopItemSerializer, GiftSerializer
from .models import Character, CharacterOwn, ShopItem, Purchase, TIER_COMMON, TIER_EPIC, TIER_RARE, TIER_LEGENDARY, ChestOpen, AnimationPurchase
from .models import TIER_SPECIAL, Gift
from .utils import create_character_own_by_id
from utils.utils import send_email, get_country_from_request

logger = logging.getLogger('django')
ZARINPAL_MERCHANT_ID = "1541656d-09fb-40cb-a635-387d73ac322d"

@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def list_my_characters(request):
    user = request.user

    owns = CharacterOwn.objects.filter(member=user)
    characters = []
    for own in owns:
        characters.append(own.character)
    
    return JsonResponse(status=200, data={"characters": CharacterSerializer(characters, many=True).data})


#Deprecated
@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def unlock_new_character(request):
    user = request.user
    sync_data = SyncData.objects.get(id=1)
    if user.coin < sync_data.unlock_character_coin:
        return JsonResponse(status=400, data={"message": "Not Enough coins."})

    owns = CharacterOwn.objects.filter(member=user)
    characters = []
    for own in owns:
        characters.append(own.character.id)
    
    not_owns = Character.objects.all().filter(unlock_with_coin=True).exclude(id__in=characters)
    if not user.is_test:
        not_owns = not_owns.exclude(is_test=True)

    if len(not_owns) == 0:
        return JsonResponse(status=401, data={"message": "You Unlocked all characters."})

    weight = []
    for not_own in not_owns:
        weight.append(not_own.random_weight)
    
    selected_character = random.choices(not_owns, weights=weight, k=1)[0]

    own = CharacterOwn()
    own.member = user
    own.character = selected_character
    own.save()

    user.coin -= sync_data.unlock_character_coin
    user.save()

    return JsonResponse(status=200, data=
                        {
                            "character": CharacterSerializer(selected_character).data,
                            "coin": user.coin,
                        })


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def unlock_new_character_v2(request):
    chest_log = ChestOpen()

    user = request.user
    data = request.data
    sync_data = SyncData.objects.get(id=1)
    price = sync_data.character_common
    if data["tier"] == TIER_RARE:
        price = sync_data.character_rare
    if data["tier"] == TIER_LEGENDARY:
        price = sync_data.character_legendary
    if data["tier"] == TIER_EPIC:
        price = sync_data.character_epic
    
    chest_log.member = user
    chest_log.price = price
    chest_log.tier = data["tier"]
    chest_log.is_exact = False

    if user.coin < price:
        chest_log.low_coin = True
        chest_log.save()
        return JsonResponse(status=400, data={"message": "Not Enough coins."})

    owns = CharacterOwn.objects.filter(member=user)
    characters = []
    for own in owns:
        if own.character.tier == data["tier"]:
            characters.append(own.character.id)
    
    not_owns = Character.objects.all().filter(unlock_with_coin=True, tier=data["tier"]).exclude(id__in=characters)
    if not user.is_test:
        not_owns = not_owns.exclude(is_test=True)

    if len(not_owns) == 0:
        chest_log.unlocked_all = True
        chest_log.save()
        return JsonResponse(status=401, data={"message": "You Unlocked all of this tier characters."})

    weight = []
    for not_own in not_owns:
        weight.append(not_own.random_weight)
    
    selected_character = random.choices(not_owns, weights=weight, k=1)[0]

    own = CharacterOwn()
    own.member = user
    own.character = selected_character
    own.save()

    user.coin -= price
    user.save()

    chest_log.success = True
    chest_log.unlocked_character = selected_character
    chest_log.save()

    return JsonResponse(status=200, data=
                        {
                            "character": CharacterSerializer(selected_character).data,
                            "coin": user.coin,
                        })


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def unlock_exact_character(request):
    chest_log = ChestOpen()

    user = request.user
    data = request.data
    character = Character.objects.get(id=data.get("character_id", 1))
    sync_data = SyncData.objects.get(id=1)
    price = sync_data.character_common_exact
    if character.tier == TIER_RARE:
        price = sync_data.character_rare_exact
    if character.tier == TIER_LEGENDARY:
        price = sync_data.character_legendary_exact
    if character.tier == TIER_EPIC:
        price = sync_data.character_epic_exact
    if character.tier == TIER_EPIC:
        price = sync_data.character_epic_exact
    if character.tier == TIER_SPECIAL:
        price = sync_data.character_special_exact

    chest_log.member = user
    chest_log.price = price
    chest_log.tier = character.tier
    chest_log.is_exact = True

    if user.coin < price:
        chest_log.low_coin = True
        chest_log.save()
        return JsonResponse(status=400, data={"message": "Not Enough coins."})

    own = CharacterOwn.objects.filter(member=user, character=character)
    if len(own) == 1:
        chest_log.unlocked_all = True
        chest_log.save()
        return JsonResponse(status=401, data={"message": "You Already Unlocked This Character"})


    own = CharacterOwn()
    own.member = user
    own.character = character
    own.save()

    user.coin -= price
    user.save()

    chest_log.success = True
    chest_log.unlocked_character = character
    chest_log.save()

    return JsonResponse(status=200, data=
                        {
                            "character": CharacterSerializer(character).data,
                            "coin": user.coin,
                        })



@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def list_shop_items(request):
    member: Member = request.user
    
    sync_data = SyncData.objects.get(id=1)
    gateway_type = "IAP"
    if member.market == MARKET_GOOGLEPLAY or member.market == MARKET_DESKTOP:
        if sync_data.shop_check_ip:
            country = get_country_from_request(request)
            logger.info("Checking Ip For Shop List: " + country)
            if country == "IR":
                gateway_type = "IPG"
            else:
                gateway_type = "IAP"
        else:
            gateway_type = "IAP"
        

        if member.force_iap:
            gateway_type = "IAP"
        if member.force_ipg:
            gateway_type = "IPG"
    

    logger.info("Checking FOR Gateway: " + gateway_type + " " + member.username)



    purchases = Purchase.objects.filter(member=member, is_test=False)
    
    if member.market == MARKET_ZARINPAL:
        sync_data = SyncData.objects.get(id=1)
        items = ShopItem.objects.filter(in_site_shop=True, is_active=True, is_test=False).order_by("-order")
        res = []
        for item in items:
            if item.one_time:
                if already_purchased(purchases=purchases, item=item):
                    continue

            price_str = int(item.price_tag * (100 - sync_data.ipg_shop_discount - member.ipg_shop_discount) / 100.0)
            price_str = "{:,}".format(price_str) + " تومان"
            res.append({
                "id": item.id,
                "title": item.title,
                "price": price_str,
                "price_str": price_str,
                "coins": item.coins,
                "one_time": item.one_time,
                "image_path": item.image_path,
                "discount": sync_data.ipg_shop_discount + member.ipg_shop_discount
            })
        return JsonResponse(status=200, data=
                            {
                                "items": res,
                                "gateway_type": "IPG",
                            })


    if member.is_test:
        items = ShopItem.objects.filter(is_active=True, hide_in_shop=False).order_by("order")
    else:
        items = ShopItem.objects.filter(is_active=True, is_test=False, hide_in_shop=False).order_by("order")
    
    res = []
    for item in items:
        if item.one_time:
            if already_purchased(purchases=purchases, item=item):
                continue
        price_str = int(item.price_tag * (100 - sync_data.ipg_shop_discount - member.ipg_shop_discount) / 100.0)
        price_str = "{:,}".format(price_str) + " تومان"
        res.append({
            "id": item.id,
            "title": item.title,
            "price": price_str,
            "coins": item.coins,
            "image": item.image_path,
            "discount": sync_data.ipg_shop_discount + member.ipg_shop_discount
        })


    return JsonResponse(status=200, data=
                        {
                            "items": ShopItemSerializer(items, many=True).data,
                            "gateway_type": gateway_type,
                        })


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def verify_purchase(request):
    member = request.user
    data = request.data
    if data.get("sku", None) == None:
        item = ShopItem.objects.get(id=data["item_id"])
    else:
        items = ShopItem.objects.filter(sku=data["sku"]).filter(is_active=True)
        if len(items) == 0:
            return JsonResponse(status=400, data=
                        {
                            "message": "Token Verification Failed.",
                        })
        item = items[0]

    prevs = Purchase.objects.filter(token=data.get("token", ""))
    if len(prevs) > 0:
        return JsonResponse(status=400, data=
                        {
                            "message": "Token Verification Failed.",
                        })

    purchase = Purchase()
    purchase.member = member
    purchase.item = item
    purchase.is_verified = False
    purchase.is_restore = data.get("restore", False)
    purchase.market = data.get("market", "CafeBazaar")
    purchase.token = data.get("token", "")
    purchase.phone_number = data.get("tel", "")
    if member.referer_content_creator:
        if member.referer_content_creator.enabled:
            purchase.referer_content_creator = member.referer_content_creator
    purchase.save()

    if purchase.market == MARKET_MYKET:
        ret = myket_verify(purchase=purchase)
        if not ret:
            return JsonResponse(status=400, data=
                {
                    "message": "Token Verification Failed.",
                })
    elif purchase.market == MARKET_BAZAAR:
        ret = bazaar_verify(purchase=purchase)
        if not ret:
            return JsonResponse(status=400, data=
                {
                    "message": "Token Verification Failed.",
                })
    else:
        return JsonResponse(status=400, data=
            {
            "message": "Token Verification Failed.",
        })


    if item.police_license:
        queues = JobQueueMember.objects.filter(member=member, type="پلیس", popped=False, cancelled=False)
        for q in queues:
            q.pop()
        member.police_job_enabled = True

    member.coin += item.coins
    member.save()
    unlocked_characters = []
    for character in item.unlock_characters.all():
        print("checking ", character, " ", character.id)
        owns = CharacterOwn.objects.filter(member=member, character=character)
        if len(owns) > 0:
            print(character, "already unlocked")
        else:
            print("unlocking ", character)
            own = CharacterOwn()
            own.member = member
            own.character = character
            own.save()
            unlocked_characters.append(character)

    #send_email.delay("<EMAIL>", "New Purchase", "{}\n{}\n{}\n{}\n".format(item.title, item.price_str, member.id, member.handle))
    return JsonResponse(status=200, data=
                        {
                            "id": item.id,
                            "coin": member.coin,
                            "characters": CharacterSerializer(unlocked_characters, many=True).data,
                        })


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def daily_reward(request):
    member = request.user
    settings = GameSettings.objects.all()[0]

    if member.last_daily_claim is None or (timezone.now() - member.last_daily_claim) >= timedelta(seconds=settings.reward_claim_interval):
        #has reward
        prizes = [100, 120,  140, 160, 180, 190, 200, 500]
        weight = [100, 100, 70, 50, 30, 20,  15,   5]
        prize = random.choices(prizes, weights=weight, k=1)[0]

        member.last_daily_claim = timezone.now()
        member.coin += prize
        member.save()

        coin_log = AddCoin()
        coin_log.member = member
        coin_log.value = prize
        coin_log.type = "DailyReward"
        coin_log.save()

        return JsonResponse(status=200, data={"prize": prize, "coins": member.coin})
    else:
        return JsonResponse(status=400, data={"message": "You don't have daily reward."})


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def unlock_animation(request):
    member = request.user
    data = request.data
    sync_data = SyncData.objects.get(id=1)
    price = sync_data.animation_price

    if member.coin < price:
        return JsonResponse(status=402, data= {"message": "Not Enough Coins"})
    

    member.coin -= price
    member.save()
    p = AnimationPurchase()
    p.member = member
    p.character = Character.objects.get(id=data["character_id"])
    p.animation_id = data["animation_id"]
    p.animation_str = data["animation_str"]
    p.save()

    purchase_log = PurchaseWithCoin()
    purchase_log.member = member
    purchase_log.price = price
    purchase_log.forgiving = False
    purchase_log.type = "Animation"
    purchase_log.secondary = str(p.animation_id)
    purchase_log.success = True
    purchase_log.save()


    return JsonResponse(status=200, data=
                        {
                            "coin": member.coin,
                            "character_id": data["character_id"],
                            "animation_id": data["animation_id"],
                            "animation_str": data["animation_str"],
                        })


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def ipg(request):
    member = request.user
    data = request.data
    
    if data.get("sku", None) == None:
        item = ShopItem.objects.get(id=data["item_id"])
    else:
        item = ShopItem.objects.get(sku=data["sku"])
    
    sync_data = SyncData.objects.get(id=1)
    price = int(item.price_tag * (100 - sync_data.ipg_shop_discount - member.ipg_shop_discount) / 100.0)
    
    response = requests.post("https://payment.zarinpal.com/pg/v4/payment/request.json", json={
        "merchant_id": ZARINPAL_MERCHANT_ID,
        "amount": price * 10,
        "callback_url": "https://api.sizakgames.ir/shop/gateway_response",
        "description": item.title + "(APP)",
        "metadata": {
            "shop_item": item.id,
            "username": member.username,
        }
    })

    if response.status_code == 200:
        auth = response.json()["data"]["authority"]
        purchase = Purchase()
        purchase.member = member
        purchase.item = item
        purchase.is_verified = False
        purchase.market = MARKET_ZARINPAL
        purchase.phone_number = data.get("tel", "")

        purchase.ongoing = True
        purchase.zarinpal_authority = auth
        purchase.fee = response.json()["data"]["fee"]
        purchase.paid_price = price

        if member.referer_content_creator:
            if member.referer_content_creator.enabled:
                purchase.referer_content_creator = member.referer_content_creator
        purchase.save()

        return JsonResponse(status=200, data={
            "url": "https://payment.zarinpal.com/pg/StartPay/{}".format(auth),
        })
    

    return JsonResponse(status=400, data={"message": "Gateway error"})


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def list_gifts(request):
    member = request.user
    
    gifts = Gift.objects.filter(member=member, have_seen=False)

    return JsonResponse(status=200, data={"gifts": GiftSerializer(gifts, many=True).data})


@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def seen_gifts(request):
    member = request.user
    
    Gift.objects.filter(member=member, have_seen=False).update(have_seen=True)

    return JsonResponse(status=200, data={"message": "Success"})



def zarinpal_callback(request):
    success = False
    prize = None

    context = {
        "line2": ""
    }
    auth = request.GET.get("Authority", "")
    purchases = Purchase.objects.filter(zarinpal_authority=auth)
    if len(purchases) == 0:
        success = False
        purchase = None
    else:
        sync_data = SyncData.objects.get(id=1)
        purchase: Purchase = purchases[0]
        purchase.ongoing = False
        purchase.is_verified = True


        context["line2"] = purchase.item.title

        response = requests.post("https://payment.zarinpal.com/pg/v4/payment/verify.json", json={
            "merchant_id": ZARINPAL_MERCHANT_ID,
            "amount": purchase.paid_price * 10,
            "authority": purchase.zarinpal_authority,
        })
        if response.status_code == 200:
            purchase.zarinpal_response = response.json()
            if response.json()["data"].get("code", 0) == 100:
                prize = purchase.apply(is_manual=False)
                success = True
                purchase.zarinpal_code  = response.json()["data"].get("code", 0)
                purchase.is_test = False
            elif response.json()["data"].get("code", 0) == 101:
                success = True
                purchase.zarinpal_code  = response.json()["data"].get("code", 0)
                purchase.is_test = False
            else:
                purchase.is_test = True
            
            purchase.save()
        else:
            purchase.is_test = True
            purchase.zarinpal_response = response.json()
            purchase.zarinpal_code = response.status_code
            purchase.save()
        


    template = loader.get_template('gateway_response.html')
    context["success"] = success
    if prize != None:
        prize["characters"] = CharacterSerializer(prize["characters"], many=True).data
        context["coin"] = prize["coin"]
    
    context["member"] = ""
    context["price"] = ""
    if purchase:
        if purchase.member:
            context["member"] = purchase.member.handle + ": " + " (" + "سکه " + str(purchase.member.coin) + ")"
        context["price"] = purchase.paid_price
        if context["price"] == 0:
            context["price"] = purchase.price
        context["price"] = "{:,}".format(context["price"]) + " تومان"


    if success:
        context["line1"] = "خرید با موفقیت انجام شد"
    else:
        context["line1"] = "خرید ناموفق"

    #<a href="intent://sizakgames.ir/#Intent;scheme=https;package=ir.sizakgames.animalrushmmo;end">بازگشت به انیمال راش</a>
    return HttpResponse(template.render(context, request))


def already_purchased(purchases, item):
    for p in purchases:
        if p.item.id == item.id:
            return True
    
    return False


def myket_verify(purchase:Purchase):
    data = {
        "tokenId": purchase.token
    }
    headers = {
        "X-Access-Token": "b3df7453-951d-42ce-9230-a8808cd79e37"
    }
    response = requests.post("https://developer.myket.ir/api/partners/applications/ir.sizakgames.animalrushmmo/purchases/products/{}/verify".format(purchase.item.sku),
                             json=data, headers=headers)
    
    purchase.zarinpal_code = response.status_code
    purchase.zarinpal_response = str(response.json())
    purchase.save()

    if response.status_code == 200:
        return True

    purchase.is_test = True
    purchase.save()
    return False


def bazaar_verify(purchase:Purchase):
    url = "https://pardakht.cafebazaar.ir/devapi/v2/api/validate/ir.sizakgames.animalrushmmo/inapp/{}/purchases/{}/".format(purchase.item.sku, purchase.token)
    bazaar_api_token = "eyJhbGciOiJIUzI1NiIsImtpZCI6ImFuY2llbnQiLCJ0eXAiOiJKV1QifQ.eyJpc3MiOiJuYXNoZXItcGlzaGtoYW4tYXBpIiwiaWF0IjoxNzQ0MjE3MDUxLCJleHAiOjQ4OTc4MTcwNTEsImFwaV9hZ2VudF9pZCI6MzYxMH0.-EBx6NYWQy6o2kgHd8HoSywiOYxMudHJAW_ZTxTvslA"
    
    headers = {
        "CAFEBAZAAR-PISHKHAN-API-SECRET": bazaar_api_token
    }
    response = requests.post(url,
                             headers=headers)
    
    
    purchase.zarinpal_code = response.status_code
    purchase.zarinpal_response = str(response.json())
    purchase.save()

    if response.status_code == 200:
        return True

    purchase.is_test = True
    purchase.save()
    return False