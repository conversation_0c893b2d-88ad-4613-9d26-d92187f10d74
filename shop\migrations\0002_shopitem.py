# Generated by Django 2.2.28 on 2023-09-27 12:02

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('shop', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ShopItem',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('removed', models.DateTimeField(blank=True, default=None, editable=False, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('last_update', models.DateTimeField(auto_now=True)),
                ('coins', models.IntegerField(default=0)),
                ('price_str', models.CharField(default='', max_length=100)),
                ('sku', models.CharField(default='', max_length=100)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
