# Generated by Django 2.2.28 on 2024-02-06 12:49

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('shop', '0015_auto_20240205_2019'),
    ]

    operations = [
        migrations.CreateModel(
            name='ChestOpen',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('removed', models.DateTimeField(blank=True, default=None, editable=False, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('last_update', models.DateTimeField(auto_now=True)),
                ('tier', models.CharField(choices=[('Common', 'Common'), ('Rare', 'Rare'), ('Legendary', 'Legendary'), ('Epic', 'Epic')], default='Common', max_length=100)),
                ('price', models.IntegerField(default=0)),
                ('success', models.BooleanField(default=False)),
                ('low_coin', models.BooleanField(default=False)),
                ('unlocked_all', models.BooleanField(default=False)),
                ('member', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('unlocked_character', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='shop.Character')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
