# Generated by Django 2.2.28 on 2024-06-19 15:17

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('shop', '0019_shopitem_one_time'),
    ]

    operations = [
        migrations.CreateModel(
            name='AnimationPurchase',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('removed', models.DateTimeField(blank=True, default=None, editable=False, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('last_update', models.DateTimeField(auto_now=True)),
                ('animation_id', models.IntegerField(default=0)),
                ('animation_str', models.CharField(default='', max_length=200)),
                ('character', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='shop.Character')),
                ('member', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
