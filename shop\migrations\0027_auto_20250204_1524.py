# Generated by Django 2.2.28 on 2025-02-04 11:54

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('shop', '0026_purchase_referer_content_creator'),
    ]

    operations = [
        migrations.AlterField(
            model_name='character',
            name='tier',
            field=models.CharField(choices=[('Common', 'Common'), ('Rare', 'Rare'), ('Legendary', 'Legendary'), ('Epic', 'Epic'), ('Special', 'Special')], default='Common', max_length=100),
        ),
        migrations.AlterField(
            model_name='chestopen',
            name='tier',
            field=models.CharField(choices=[('Common', 'Common'), ('Rare', 'Rare'), ('Legendary', 'Legendary'), ('Epic', 'Epic'), ('Special', 'Special')], default='Common', max_length=100),
        ),
    ]
