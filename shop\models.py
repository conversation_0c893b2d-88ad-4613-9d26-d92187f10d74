from django.db import models
from django.utils import timezone
from utils.models import BaseModel

TIER_COMMON = "Common"
TIER_RARE = "Rare"
TIER_LEGENDARY = "Legendary"
TIER_EPIC = "Epic"
TIER_SPECIAL = "Special"

TIER_CHOICES = (
    (TIER_COMMON, TIER_COMMON),
    (TIER_RARE, TIER_RARE),
    (TIER_LEGENDARY, TIER_LEGENDARY),
    (TIER_EPIC, TIER_EPIC),
    (TIER_SPECIAL, TIER_SPECIAL),
)


class Character(BaseModel):
    name = models.Char<PERSON>ield(max_length=100, default='')
    path = models.CharField(max_length=200, default='', blank=True)
    card_path = models.CharField(max_length=200, default='', blank=True)
    random_weight = models.IntegerField(default=1)
    members = models.ManyToManyField('account.Member', through='CharacterOwn')
    unlock_with_coin = models.BooleanField(default=True)
    tier = models.CharField(max_length=100, default=TIER_COMMON, choices=TIER_CHOICES)
    is_test = models.BooleanField(default=False)


    def __str__(self):
        return self.name


class CharacterOwn(BaseModel):
    member = models.ForeignKey('account.Member', on_delete=models.CASCADE, related_name='characters')
    character = models.ForeignKey(Character, on_delete=models.CASCADE)

    def __str__(self) -> str:
        return self.member.__str__() + " " + self.character.__str__()
    

    @property
    def handle(self):
        return self.member.handle


class ShopItem(BaseModel):
    title = models.CharField(default="", max_length=100)
    title_google = models.CharField(default="", max_length=100)
    coins = models.IntegerField(default=0)
    price_str = models.CharField(default="", max_length=100)
    price_str_google = models.CharField(default="", max_length=100)
    price_tag = models.IntegerField(default=1000)
    sku = models.CharField(default="", max_length=100)
    is_active = models.BooleanField(default=True)
    police_license = models.BooleanField(default=False)
    order = models.IntegerField(default=1)
    unlock_characters = models.ManyToManyField(Character, blank=True)
    one_time = models.BooleanField(default=False)
    police_license = models.BooleanField(default=False)
    image_path = models.CharField(default="", max_length=200)
    is_test = models.BooleanField(default=False)
    hide_in_shop = models.BooleanField(default=False)
    in_site_shop = models.BooleanField(default=True)


    def __str__(self) -> str:
        return " (" + self.sku + "): " + str(self.coins) + " " + str(self.price_str)


MARKET_BAZAAR = "CafeBazaar"
MARKET_MYKET = "Myket"
MARKET_ZARINPAL = "Zarinpal"
MARKET_GOOGLEPLAY = "GooglePlay"
MARKET_DESKTOP = "Desktop"

MARKET_CHOICES = (
    (MARKET_BAZAAR, MARKET_BAZAAR),
    (MARKET_MYKET, MARKET_MYKET),
    (MARKET_ZARINPAL, MARKET_ZARINPAL),
    (MARKET_GOOGLEPLAY, MARKET_GOOGLEPLAY),
    (MARKET_DESKTOP, MARKET_DESKTOP),
)

class Purchase(BaseModel):
    member = models.ForeignKey('account.member', on_delete=models.CASCADE)
    item = models.ForeignKey(ShopItem, on_delete=models.CASCADE)
    is_verified = models.BooleanField(default=True)
    is_manual = models.BooleanField(default=False)
    is_test = models.BooleanField(default=False)
    market = models.CharField(max_length=100, choices=MARKET_CHOICES, default=MARKET_MYKET)
    referer_content_creator = models.ForeignKey('account.ContentCreator', on_delete=models.CASCADE, null=True, blank=True, default=None, related_name="shop_referers")
    zarinpal_authority = models.CharField(max_length=100, default="", blank=True)
    zarinpal_response = models.TextField(default="", blank=True)
    zarinpal_code = models.IntegerField(default=0)
    ongoing = models.BooleanField(default=False)#Zarinpal
    fee = models.IntegerField(default=0)
    paid_price = models.IntegerField(default=0)
    is_gift = models.BooleanField(default=False)
    gift_text = models.TextField(default="", blank=True)
    have_seen = models.BooleanField(default=False)
    token = models.CharField(max_length=100, default="", blank=True)
    from_site = models.BooleanField(default=False)
    phone_number = models.CharField(max_length=20, blank=True, default='')
    is_restore = models.BooleanField(default=False)


    @property
    def price(self):
        return self.item.price_tag

    @property
    def handle(self):
        return self.member.handle


    def apply(self, is_manual=True):
        self.member.coin += self.item.coins
        if self.item.police_license:
            self.member.police_job_enabled = True
        self.member.save()
        if is_manual:
            self.is_manual = True
        if self.member.referer_content_creator:
            if self.member.referer_content_creator.enabled:
                self.referer_content_creator = self.member.referer_content_creator
        self.save()
        unlocked_characters = []
        for character in self.item.unlock_characters.all():
            owns = CharacterOwn.objects.filter(member=self.member, character=character)
            if len(owns) > 0:
                print(character, "already unlocked")
            else:
                print("unlocking ", character)
                own = CharacterOwn()
                own.member = self.member
                own.character = character
                own.save()
                unlocked_characters.append(character)
        

        if self.is_gift:
            gifts = Gift.objects.filter(purchase=self)
            if len(gifts) == 0:
                gift = Gift()
                gift.purchase = self
                gift.member = self.member
                gift.have_seen = False
                gift.save()

        return {
            "coin": self.item.coins,
            "characters": unlocked_characters,
        }


class ChestOpen(BaseModel):
    member = models.ForeignKey('account.Member', on_delete=models.CASCADE)
    tier = models.CharField(max_length=100, default=TIER_COMMON, choices=TIER_CHOICES)
    price = models.IntegerField(default=0)
    success = models.BooleanField(default=False)
    low_coin = models.BooleanField(default=False)
    unlocked_all = models.BooleanField(default=False)
    unlocked_character = models.ForeignKey(Character, on_delete=models.CASCADE, null=True, default=None, blank=True)
    is_exact = models.BooleanField(default=False)


    def __str__(self) -> str:
        return self.member.__str__() + " " + self.character.__str__()


    @property
    def handle(self):
        return self.member.handle


class AnimationPurchase(BaseModel):
    member = models.ForeignKey('account.member', on_delete=models.CASCADE)
    character = models.ForeignKey(Character, on_delete=models.CASCADE)
    animation_id = models.IntegerField(default=0)
    animation_str = models.CharField(max_length=200, default="")

    @property
    def handle(self):
        return self.member.handle


class Gift(BaseModel):
    member = models.ForeignKey('account.member', on_delete=models.CASCADE)
    purchase = models.ForeignKey(Purchase, on_delete=models.CASCADE, blank=True, null=True)
    manual_coins = models.IntegerField(default=100)
    manual_text = models.TextField(default="", blank=True)
    have_seen = models.BooleanField(default=False)


    @property
    def price(self):
        if self.purchase:
            return self.purchase.paid_price
        return 0


    @property
    def coins(self):
        if self.purchase:
            return self.purchase.item.coins

        return self.manual_coins
    

    @property
    def text(self):
        if self.purchase:
            return self.purchase.gift_text

        return self.manual_text
