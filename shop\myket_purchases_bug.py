from shop.models import *
from django.db.models import Q

purchases = Purchase.objects.filter(Q(market=MARKET_MYKET) | Q(market=MARKET_BAZAAR)).filter(id__gt=91384).filter(is_test=False).filter(is_manual=False).filter(zarinpal_code=0)

p1:Purchase
for p1 in purchases:
    if len(p1.token) < 5:
        continue
    if p1.is_test:
        continue
    for p2 in purchases:
        if p1.id == p2.id:
            continue
        if p1.token == p2.token:
            p2.is_test = True
            p2.save()
    print("apply: ", p1.id, " ", p1.member.username, " ", p1.token)
    #p1.apply()
