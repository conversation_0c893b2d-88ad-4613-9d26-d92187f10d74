from rest_framework import serializers
from .models import CharacterOwn, Character, ShopItem, Gift

class CharacterSerializer(serializers.ModelSerializer):
    class Meta:
        model = Character
        fields = ("id", "name", "path", "card_path", "tier")


class CharacterOwnSerializer(serializers.ModelSerializer):
    class Meta:
        model = CharacterOwn
        fields = ("id", "member", "character")


class ShopItemSerializer(serializers.ModelSerializer):
    class Meta:
        model = ShopItem
        fields = ("id", "sku", "price_str", "coins", "image_path", "unlock_characters", "title", "one_time", "price_str_google", "title_google")


class GiftSerializer(serializers.ModelSerializer):
    class Meta:
        model = Gift
        fields = ("id", "coins", "text")