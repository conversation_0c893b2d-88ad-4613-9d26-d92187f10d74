import random, logging, requests, json
from datetime import timed<PERSON><PERSON>
from rest_framework import permissions
from rest_framework.decorators import api_view, permission_classes, renderer_classes
from rest_framework_jwt.settings import api_settings
from rest_framework_swagger import renderers
from django.http.response import JsonResponse
from django.utils import timezone
from django.shortcuts import render
from django.http import HttpResponse
from django.template import loader

from account.models.member import Member, MARKET_GOOGLEPLAY, MARKET_ZARINPAL
from account.models.sync_data import SyncData
from game.models import GameSettings, PurchaseWithCoin, AddCoin
from police.models import JobQueueMember
from .serializers import CharacterSerializer, ShopItemSerializer
from .models import Character, CharacterOwn, ShopItem, Purchase, TIER_COMMON, TIER_EPIC, TIER_RARE, TIER_LEGENDARY, ChestOpen, AnimationPurchase
from .models import TIER_SPECIAL
from .utils import create_character_own_by_id
from utils.utils import send_email, get_country_from_request

logger = logging.getLogger('django')
ZARINPAL_MERCHANT_ID = "1541656d-09fb-40cb-a635-387d73ac322d"

@api_view(['POST', ])
@permission_classes([])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def site_list_shop_items(request):
    data = request.data

    username = data.get("username", "")
    members = Member.objects.filter(username=username)
    if len(members) == 0:
        return JsonResponse(status=400, data={"message": "Invalid Username."})
    member: Member = members[0]
    
    sync_data = SyncData.objects.get(id=1)
    purchases = Purchase.objects.filter(member=member, is_test=False)
    items = ShopItem.objects.filter(in_site_shop=True, is_active=True, is_test=False).order_by("-order")
    res = []
    for item in items:
        if item.one_time:
            if already_purchased(purchases=purchases, item=item):
                continue
        price_str = int(item.price_tag * (100 - sync_data.ipg_shop_discount - member.ipg_shop_discount) / 100.0)
        price_str = "{:,}".format(price_str) + " تومان"
        res.append({
            "id": item.id,
            "title": item.title,
            "price": price_str,
            "coins": item.coins,
            "image": item.image_path,
            "discount": sync_data.ipg_shop_discount + member.ipg_shop_discount
        })
    return JsonResponse(status=200, data=
                        {
                            "items": res,
                            "handle": member.handle,
                            "coins": member.coin
                        })


@api_view(['POST', ])
@permission_classes([])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def site_shop_ipg(request):
    member = request.user
    data = request.data

    tel = data.get("tel", None)
    if  tel is None or len(tel) < 3:
        return JsonResponse(status=401, data={"message": "شماره تلفن غلط است"})
    

    username = data.get("username", "")
    members = Member.objects.filter(username=username)
    if len(members) == 0:
        return JsonResponse(status=400, data={"message": "Invalid Username."})
    member: Member = members[0]

    items = ShopItem.objects.filter(id=data.get("item_id", 0))
    if len(items) == 0:
        return JsonResponse(status=400, data={"message": "Invalid Shop item ."})
    item = items[0]

    sync_data = SyncData.objects.get(id=1)
    price = int(item.price_tag * (100 - sync_data.ipg_shop_discount - member.ipg_shop_discount) / 100.0)
    
    response = requests.post("https://payment.zarinpal.com/pg/v4/payment/request.json", json={
        "merchant_id": ZARINPAL_MERCHANT_ID,
        "amount": price * 10,
        "callback_url": "https://api.sizakgames.ir/shop/gateway_response",
        "description": item.title + " (SITE)",
        "metadata": {
            "shop_item": item.id,
            "username": member.username,
            "from_site": False
        }
    })

    if response.status_code == 200:
        auth = response.json()["data"]["authority"]
        purchase = Purchase()
        purchase.member = member
        purchase.item = item
        purchase.is_verified = False
        purchase.market = MARKET_ZARINPAL
        purchase.paid_price = price
        purchase.is_gift = data.get("gift", False)
        purchase.gift_text = data.get("gift_text", "")
        purchase.phone_number = data.get("tel", "")
        purchase.from_site = True

        purchase.ongoing = True
        purchase.zarinpal_authority = auth
        purchase.fee = response.json()["data"]["fee"]

        if member.referer_content_creator:
            if member.referer_content_creator.enabled:
                purchase.referer_content_creator = member.referer_content_creator
        purchase.save()

        if data.get("tel", None) is None:
            mail_body = member.username + "\n"
            mail_body += json.dumps(data)
            send_email.delay("<EMAIL>", "No Tel", mail_body)

        return JsonResponse(status=200, data={
            "url": "https://payment.zarinpal.com/pg/StartPay/{}".format(auth),
        })
    

    return JsonResponse(status=400, data={"message": "Gateway error"})


def already_purchased(purchases, item):
    for p in purchases:
        if p.item.id == item.id:
            return True
    
    return False