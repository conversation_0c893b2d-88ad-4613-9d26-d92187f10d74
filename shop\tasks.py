import requests
from game_backend.celery import app
from django.utils import timezone
from datetime import timedelta

from account.models.sync_data import SyncData
from .models import Purchase, MARKET_ZARINPAL, MARKET_MYKET, MARKET_BAZAAR
from utils.utils import send_email
from django.db.models import Q

ZARINPAL_MERCHANT_ID = "1541656d-09fb-40cb-a635-387d73ac322d"

@app.task
def verify_zarinpal_zombies():
    purchases = Purchase.objects.filter(market=MARKET_ZARINPAL).filter(id__gt=90000).exclude(zarinpal_code__in=[100, 101, 401])

    sync_data = SyncData.objects.get(id=1)
    for purchase in purchases:
        if (timezone.now() - purchase.created).total_seconds() < 15 * 60:
            continue
        
        price = int(purchase.item.price_tag * (100 - sync_data.ipg_shop_discount - purchase.member.ipg_shop_discount) / 100.0)
        purchase.ongoing = False
        purchase.is_verified = True


        response = requests.post("https://payment.zarinpal.com/pg/v4/payment/verify.json", json={
            "merchant_id": ZARINPAL_MERCHANT_ID,
            "amount": purchase.paid_price * 10,
            "authority": purchase.zarinpal_authority,
        })
        if response.status_code == 200:
            purchase.zarinpal_response = response.json()
            if response.json()["data"].get("code", 0) == 100:
                prize = purchase.apply(is_manual=False)
                success = True
                purchase.is_test = False
                purchase.zarinpal_code  = response.json()["data"].get("code", 0)
            elif response.json()["data"].get("code", 0) == 101:
                success = True
                purchase.zarinpal_code  = response.json()["data"].get("code", 0)
                purchase.is_test = False
            else:
                purchase.is_test = True
            
            purchase.save()
        else:
            purchase.zarinpal_response = response.json()
            purchase.zarinpal_code = response.status_code
            purchase.is_test = True
            purchase.save()


@app.task
def apply_market_purchases():
    purchases = Purchase.objects.filter(Q(market=MARKET_MYKET) | Q(market=MARKET_BAZAAR)).filter(id__gt=91384).filter(is_test=False).filter(is_manual=False).filter(zarinpal_code=0)

    p1:Purchase
    for p1 in purchases:
        if len(p1.token) < 5:
            continue
        if p1.is_test:
            continue
        for p2 in purchases:
            if p1.id == p2.id:
                continue
            if p1.token == p2.token:
                #print("duplicate")
                p2.is_test = True
                p2.save()
        p1.apply()

