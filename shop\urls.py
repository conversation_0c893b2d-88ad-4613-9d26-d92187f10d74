from django.urls import path
from .function_views import list_my_characters, unlock_new_character, list_shop_items, verify_purchase, daily_reward, unlock_new_character_v2, unlock_animation
from .function_views import unlock_exact_character, zarinpal_callback, ipg, list_gifts, seen_gifts
from .site_shop_views import site_list_shop_items, site_shop_ipg

urlpatterns = [
    path(r'list_my_characters/', list_my_characters, name="list_my_characters"),
    path(r'unlock_new_character/', unlock_new_character, name="unlock_new_character"),
    path(r'unlock_new_character_v2/', unlock_new_character_v2, name="unlock_new_character_v2"),
    path(r'list_shop/', list_shop_items, name="list_shop_items"),
    path(r'verify_purchase/', verify_purchase, name="verify_purchase"),
    path(r'daily_reward/', daily_reward, name="daily_reward"),
    path(r'unlock_animation/', unlock_animation, name="unlock_animation"),
    path(r'unlock_exact_character/', unlock_exact_character, name="unlock_exact_character"),
    path(r'gateway_response/', zarinpal_callback, name="gateway_response"),
    path(r'ipg/', ipg, name="ipg"),
    path(r'list_gifts/', list_gifts, name="list_gifts"),
    path(r'seen_gifts/', seen_gifts, name="seen_gifts"),

    path(r'site_list_items/', site_list_shop_items, name="site_list_items"),
    path(r'site_ipg/', site_shop_ipg, name="site_ipg"),
]
