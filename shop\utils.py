from .models import CharacterOwn, Character

def create_freddy_character_own(member):
    freddy = Character.objects.get(id=1)
    own = CharacterOwn()
    own.member = member
    own.character = freddy
    own.save()


def create_parinaz_character_own(member):
    try:
        parinaz = Character.objects.get(id=35)
        own = CharacterOwn()
        own.member = member
        own.character = parinaz
        own.save()
    except:
        return


def create_character_own_by_id(member, character_id):
    try:
        character = Character.objects.get(id=character_id)
        own = CharacterOwn()
        own.member = member
        own.character = character
        own.save()
    except:
        return
