#!/usr/bin/env python3
"""
Test script for the end_prophunt_game endpoint
"""

import json
import requests

# Example request data from the user
test_data = {
    "player_stats": {
        "322058945": {
            "backend_id": 9,
            "hp_remaining": 100,
            "score": 11,
            "survived": True,
            "team": 1,
            "total_kill": 0
        },
        "1617953035": {
            "backend_id": 9,
            "hp_remaining": 100,
            "score": 7,
            "survived": True,
            "team": 0,
            "total_kill": 0
        }
    },
    "rounds": [
        {
            "round_time": 2.00833333333333,
            "total_haunters": 1,
            "total_props": 1,
            "winner_team": 0
        },
        {
            "round_time": 2.00833333333333,
            "total_haunters": 1,
            "total_props": 1,
            "winner_team": 0
        },
        {
            "round_time": 2.00833333333333,
            "total_haunters": 1,
            "total_props": 1,
            "winner_team": 0
        }
    ]
}

def test_end_prophunt_game():
    """Test the end_prophunt_game endpoint"""
    url = "http://localhost:8000/prophunt/end_game/"  # Adjust URL as needed
    
    headers = {
        'Content-Type': 'application/json',
    }
    
    try:
        response = requests.post(url, json=test_data, headers=headers)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")

        if response.status_code == 200:
            print("✅ Test passed!")
            response_data = response.json()
            game_data = response_data.get('game', {})
            print(f"\n📊 Round Win Summary:")
            print(f"Props (Team 0) round wins: {game_data.get('props_round_wins', 0)}")
            print(f"Hunters (Team 1) round wins: {game_data.get('hunters_round_wins', 0)}")
            print(f"Overall winning team: {game_data.get('winning_team', 'Unknown')}")
        else:
            print("❌ Test failed!")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
    except json.JSONDecodeError as e:
        print(f"❌ JSON decode error: {e}")
        print(f"Raw response: {response.text}")

if __name__ == "__main__":
    test_end_prophunt_game()
