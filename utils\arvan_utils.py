import boto3
import logging
from botocore.exceptions import ClientError


logging.basicConfig(level=logging.INFO)

endpoint_url = 'https://s3.ir-thr-at1.arvanstorage.com/'
aws_access_key_id = 'df576247-dfdc-4268-a756-5bd187cee00f'
aws_secret_access_key = '24a668edb12b3844a3ae9510a831649a3c1d0fb690160d91a30fb0f382ff8e31'

BUCKET_APK = {
    "name": "kariz-apks",
    "url": "https://kariz-apks.s3.ir-thr-at1.arvanstorage.com/"
}
BUCKET_ICON = {
    "name": "kariz-disk",
    "url": "https://kariz-disk.s3.ir-thr-at1.arvanstorage.com/"
}


def list_buckets():
    try:
        s3_resource = boto3.resource(
            's3',
            endpoint_url = endpoint_url,
            aws_access_key_id = aws_access_key_id,
            aws_secret_access_key = aws_secret_access_key
        )
    except Exception as exc:
        logging.info(exc)
    else:
        try:
            for bucket in s3_resource.buckets.all():
                logging.info(f'bucket_name: {bucket.name}')
        except ClientError as exc:
            logging.error(exc)


def upload_file(path, name, bucket_dict):
    try:
        s3_resource = boto3.resource(
            's3',
            endpoint_url = endpoint_url,
            aws_access_key_id = aws_access_key_id,
            aws_secret_access_key = aws_secret_access_key
        )
    except Exception as exc:
        logging.error(exc)
    else:
        try:
            bucket = s3_resource.Bucket(bucket_dict["name"])
            file_path = path
            object_name = name

            with open(file_path, "rb") as file:
                res = bucket.put_object(
                    ACL='public-read',
                    Body=file,
                    Key=object_name
                )

            return bucket_dict["url"] + name
        except ClientError as e:
            logging.error(e)
