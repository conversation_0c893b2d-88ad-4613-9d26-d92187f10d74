from game_backend.celery import app
import random
import string, requests
from django.core.mail import send_mail, EmailMessage
from django.conf import settings

BROKER_TOKEN = "JWT eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************.74er7CVPVEr6b2OXNpimWWWw_YZFND6LfFMVUsUdpyM"


def number_generator(size=6, chars=string.digits):
    return ''.join(random.choice(chars) for _ in range(size))


def code_generator(size=6, chars=string.ascii_letters):
    return ''.join(random.choice(chars) for _ in range(size))


def slug_generator(size=8, chars=string.ascii_lowercase):
    return ''.join(random.choice(chars) for _ in range(size))


@app.task
def send_email(receiver, title, body, tag="Normal"):
    email = EmailMessage(
        title,
        body,
        settings.EMAIL_MAIL_FROM,
        [receiver],
        headers={"x-liara-tag": tag}
    )
    
    email.send(fail_silently=False)


def ip_details(ip):
    url = "http://ip-api.com/json/"

    response = requests.get(url + "{}".format(ip))
    if response.status_code == 200:
        return response.json().get("countryCode", "US")
    
    return "US"


def get_ip_from_request(request):
    remote_addr = request.META.get('HTTP_X_FORWARDED_FOR')
    if remote_addr:
        address = remote_addr.split(',')[-1].strip()
    else:
        address = request.META.get('REMOTE_ADDR')
    

    return address


def get_country_from_request(request):
    return ip_details(get_ip_from_request(request))


def check_inventory_ban(member) -> bool:
    """
    Check if the member has an inventory ban.
    
    Args:
        member (Member): The member to check.
    
    Returns:
        bool: True if the member has an inventory ban, False otherwise.
    """
    if not member.data:
        return False

    if not member.data.first():
        return False
    
    data = member.data.first().data
    inventory = data["inventory"]["inventory"]
    if len(inventory) > 3:
        return True
    
    for item in inventory:
        if item is None:
            continue
        count = item.get("count", 0)
        if not type(count) is int:
            return True
        if count > 40:
            return True

    return False


def inventory_check(member, member_data) -> bool:
    inventory = member_data["inventory"]["inventory"]
    if len(inventory) > 3:
        member.full_ban = True
        member.save()
        return True
    
    for item in inventory:
        if item is None:
            continue
        count = item.get("count", 0)
        if not type(count) is int:
            member.full_ban = True
            member.save()
            return True
        if count > 40:
            member.full_ban = True
            member.save()
            return True

    return False
