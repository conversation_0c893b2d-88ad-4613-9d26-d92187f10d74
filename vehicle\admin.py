from django.contrib import admin
from utils.admin import BaseAdmin

from .models import Vehicle, VehicleOwn


class VehicleAdmin(BaseAdmin):
    list_display = ('id', 'name', 'order', 'price', 'price1w', 'price1d', 'max_fuel', 'max_hp', 'is_test', 'police_license', 'min_version')
    list_editable = ('order', 'is_test', 'price', 'price1w', 'price1d', 'min_version')
    list_filter = ()
    


class VehicleOwnAdmin(BaseAdmin):
    list_display = ('id', 'created', 'member', 'vehicle', 'remaining', 'fuel', 'hp', 'in_garage')
    list_editable = ()
    list_filter = ('vehicle',)
    raw_id_fields = ('member',)
    search_fields = ('member__handle', 'member__username')


admin.site.register(Vehicle, VehicleAdmin)
admin.site.register(VehicleOwn, VehicleOwnAdmin)