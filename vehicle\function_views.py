import logging
from datetime import timed<PERSON>ta

from rest_framework import permissions
from rest_framework.decorators import api_view, permission_classes, renderer_classes
from rest_framework_jwt.settings import api_settings
from rest_framework_swagger import renderers
from django.http.response import JsonResponse
from django.utils import timezone

from account.models.member import Member
from game.models import PurchaseWithCoin, GameServer
from .models import Vehicle, VehicleOwn
from .serializers import VehicleOwnSerializer, VehicleSerializer


logger = logging.getLogger('django')


#Client
@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def list_my_vehicles(request):
    member:Member = request.user
    owns = VehicleOwn.objects.filter(member=member)

    results = []
    own: VehicleOwn
    for own in owns:
        if own.is_valid:
            results.append(own)
    

    if member.is_test:
        vehicles = Vehicle.objects.all().filter(min_version__lte=member.game_version).order_by("order")
    else:
        vehicles = Vehicle.objects.all().filter(is_test=False).filter(min_version__lte=member.game_version).order_by("order")

    return JsonResponse(status=200, data={
        "my_vehicles": VehicleOwnSerializer(results, many=True).data,
        "vehicles": VehicleSerializer(vehicles, many=True).data,
    })


#Client
@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def purchase_vehicle(request):
    member:Member = request.user
    data = request.data

    vehicle:Vehicle = Vehicle.objects.get(id=data.get("vehicle_id", 1))
    
    price = vehicle.price
    time = None
    #full-1w-1d
    if data.get("type", "full") == "full":
        price = vehicle.price
        time = None
    elif data.get("type", "full") == "1w":
        price = vehicle.price1w
        time = timedelta(weeks=1)
    elif data.get("type", "full") == "1d":
        price = vehicle.price1d
        time = timedelta(days=1)
    

    if vehicle.police_license:
        if not member.police_job_enabled:
            return JsonResponse(status=406, data={"message": "Need Police License."})
    

    if price == -1:
        return JsonResponse(status=405, data={"message": "Not Selling This Type Of Rent."})


    if price > member.coin:
        return JsonResponse(status=400, data={"message": "Not Enough Coins"})
    

    owns = VehicleOwn.objects.filter(member=member, vehicle=vehicle)
    own = None
    if len(owns) > 0:
        own = owns[0]
        if own.valid_time is None:
            return JsonResponse(status=401, data={"message": "Already Bought"})
    
    if own is None:
        own = VehicleOwn()
        own.member = member
        own.vehicle = vehicle
    
    if time is None:
        own.valid_time = None
    else:
        if own.valid_time is None:
            own.valid_time = timezone.now()
        if not own.is_valid:
            own.valid_time = timezone.now()
        
        
        own.valid_time = own.valid_time + time
    
    if data.get("type", "full") == "full":
        own.full_purchase = True
    
    own.save()
    member.coin -= price
    member.save()

    purchase_log = PurchaseWithCoin()
    purchase_log.member = member
    purchase_log.price = price
    purchase_log.forgiving = False
    purchase_log.type = "Vehicle"
    purchase_log.secondary = vehicle.name + " " + data.get("type", "")
    purchase_log.success = True
    purchase_log.save()


    return JsonResponse(status=200, data={
        "message": "success",
        "coin": member.coin,
        })


#Gameserver
@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def update_vehicle(request):
    data = request.data

    server_key = data.get("server_key", "")
    gss = GameServer.objects.filter(server_key=server_key)
    if len(gss) == 0:
        return JsonResponse(status=400, data={
                    "message": "GTFO1"
                })
    gs:GameServer = gss[0]
    if not gs.trusted:
        return JsonResponse(status=400, data={
                    "message": "GTFO2"
                })
    
    members = Member.objects.filter(id=data.get("player_backend_id", 0))
    if len(members) == 0:
        return JsonResponse(status=400, data={
                    "message": "Member not found"
                })
    member:Member = members[0]
    owns = VehicleOwn.objects.filter(member=member, vehicle=data.get("vehicle_id", 0))
    if len(owns) == 0:
        return JsonResponse(status=400, data={
                    "message": "Vehicle own not found"
                })

    own = owns[0]
    own.fuel = data.get("fuel", 0)
    own.hp = data.get("hp", 0)
    own.in_garage = data.get("in_garage", True)
    own.save()

    return JsonResponse(status=200, data={})


#Gameserver
@api_view(['POST', ])
@permission_classes([permissions.IsAuthenticated])
@renderer_classes([renderers.OpenAPIRenderer, renderers.JSONRenderer])
def spawn_vehicle_check(request):
    data = request.data

    data = request.data

    id = data.get("backend_id", "")
    members = Member.objects.filter(id=id)

    if len(members) == 0:
        return JsonResponse(status=400, data={"message": "Invalid Member"})
    
    member:Member = members[0]
    if not member.vehicle_spawn:
        return JsonResponse(status=401, data={"message": "You don't have license"})
    if member.full_ban:
        return JsonResponse(status=401, data={"message": "You don't have license"})
    

    owns = VehicleOwn.objects.filter(member=member, vehicle=data.get("vehicle_id", 0))
    if len(owns) == 0:
        return JsonResponse(status=402, data={
                    "message": "You don't own vehicle"
                })
    own = owns[0]
    if own.remaining == -1:
        return JsonResponse(status=200, data={})

    if own.remaining < 0:
        return JsonResponse(status=403, data={"message": "Vehicle Expired."})
    
    return JsonResponse(status=200, data={})
