from django.db import models
from django.utils import timezone
from utils.models import BaseModel


class Vehicle(BaseModel):
    name = models.CharField(max_length=100, default="", blank=True)
    max_fuel = models.FloatField(default=100)
    max_hp = models.FloatField(default=100)
    max_speed = models.FloatField(default=70)
    
    price = models.IntegerField(default=100000)
    price1d = models.IntegerField(default=3000)
    price1w = models.IntegerField(default=10000)

    order = models.IntegerField(default=1)
    police_license = models.BooleanField(default=False)
    is_test = models.BooleanField(default=False)
    min_version = models.IntegerField(default=130)

    def __str__(self) -> str:
        return self.name


class VehicleOwn(BaseModel):
    member = models.ForeignKey('account.Member', on_delete=models.CASCADE, related_name='vehicles')
    vehicle = models.ForeignKey(Vehicle, on_delete=models.CASCADE)
    valid_time = models.DateTimeField(default=timezone.now, blank=True, null=True)
    fuel = models.FloatField(default=100)
    hp = models.FloatField(default=100)
    in_garage = models.BooleanField(default=True)
    full_purchase = models.BooleanField(default=False)


    def __str__(self) -> str:
        return self.member.__str__() + " " + self.vehicle.__str__()


    @property
    def is_valid(self):
        if self.remaining == -1:
            return True
        
        return self.valid_time > timezone.now()


    @property
    def remaining(self):
        if self.full_purchase:
            return -1

        return (self.valid_time - timezone.now()).total_seconds()
