from rest_framework import serializers
from .models import Vehicle, VehicleOwn

class VehicleSerializer(serializers.ModelSerializer):
    class Meta:
        model = Vehicle
        fields = ("id", "price", "price1w", "price1d", "name", "max_fuel", "max_hp", "max_speed", "order")


class VehicleOwnSerializer(serializers.ModelSerializer):
    vehicle = VehicleSerializer(read_only = True)

    class Meta:
        model = VehicleOwn
        fields = ("id", "vehicle", "remaining", "hp", "fuel")
